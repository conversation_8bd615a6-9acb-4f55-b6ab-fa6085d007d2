import type { iString } from '@bika/types/system';

export const SpaceNotificationMailI18n: { [key: string]: iString } = {
  copyright: {
    en: `Copyright © ${new Date().getFullYear()} Bika.ai, All rights reserved`,
    'zh-CN': `版权所有 © ${new Date().getFullYear()} Bika.ai`,
    'zh-TW': `版權所有 © ${new Date().getFullYear()} Bika.ai，保留所有權利`,
    ja: `著作権 © ${new Date().getFullYear()} Bika.ai、全著作権所有`,
  },
  address: {
    en: '488 The Bridle Walk, Toronto, ON L6C 2Y4 Canada',
    'zh-CN': '加拿大多伦多 L6C 2Y4 号 488 The Bridle Walk',
    'zh-TW': '加拿大安大略省多倫多市 The Bridle Walk 488號 L6C 2Y4',
    ja: 'カナダ、トロント、L6C 2Y4、488 The Bridle Walk',
  },
  subscribe: {
    en: 'Want to change how you receive these emails? <br /> You can update your preferences or unsubscribe from this list.',
    'zh-CN': '想要更改如何接收这些邮件吗？<br /> 您可以更新您的偏好或取消订阅此列表。',
    'zh-TW': '想要更改您接收這些電子郵件的方式嗎？<br />您可以更新您的偏好或從該列表中退訂。',
    ja: 'これらのメールの受信方法を変更したいですか？<br /> プリファレンスを更新するか、このリストから登録解除することができます。',
  },
  default_button_text: {
    en: 'View',
    'zh-CN': '查看',
    'zh-TW': '查看',
    ja: '表示',
  },
};
