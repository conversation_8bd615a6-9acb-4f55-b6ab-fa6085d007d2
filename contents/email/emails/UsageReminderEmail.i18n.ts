import { Locale } from '@bika/types/system';

const UsageReminderEmailI18n: { [_l in Locale]: { [k: string]: string } } = {
  en: {
    subject: 'Important: Usage Limit Exceeded',
    template:
      'Usage limit exceeded remind: Your space {name} [{feature}] specification has exceeded the maximum limit of {limit}, and currently used {used}',
    button: 'Upgrade now',
  },
  'zh-TW': {
    subject: '重要: 用量已超出限制',
    template: '用量超出提醒: 您的空間 {name} [{feature}] 規格已超出最大限制 {limit}，目前已使用 {used}',
    button: '立即升級',
  },
  'zh-CN': {
    subject: '重要: 用量已超出限制',
    template: '用量超出提醒: 您的空间 {name} [{feature}] 规格已超出最大限制 {limit}，目前已使用 {used}',
    button: '立即升级',
  },
  ja: {
    subject: '重要: 使用量が制限を超えました',
    template:
      '使用量が制限を超えました: スペース {name} の [{feature}] 仕様が最大制限 {limit} を超えており、現在使用中 {used}',
    button: '今すぐアップグレード',
  },
};

export default UsageReminderEmailI18n;
