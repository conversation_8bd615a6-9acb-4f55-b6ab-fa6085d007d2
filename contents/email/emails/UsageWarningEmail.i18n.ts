import { Locale } from '@bika/types/system';

/**
 * 低用量预警
 */
const UsageWarningEmailI18n: { [_l in Locale]: { [k: string]: string } } = {
  'zh-CN': {
    subject: '重要: 您的空间站功能用量已低于{ratio}%',
    template:
      '尊敬的用户, 我们检测到您当前订阅计划 「{planName}」 中的功能 「{featureName}」 用量已低于 {ratio}%。 截止当前已使用 {used}，最大限制值为 {limit} ,请及时关注以避免对后续使用造成影响',
    button: '立即升级',
  },
  'zh-TW': {
    subject: '重要: 您的空間站功能用量已低於{ratio}%',
    template:
      '尊敬的用戶, 我們檢測到您當前訂閱計劃 「{planName}」 中的功能 「{featureName}」 用量已低於 {ratio}%。 截止當前已使用 {used}，最大限制值為 {limit} ,請及時關注以避免對後續使用造成影響',
    button: '立即升級',
  },
  en: {
    subject: 'Important: Your space station feature usage has fallen below {ratio}%',
    template:
      'Dear user, we have detected that the usage of the feature 「{featureName}」 in your current subscription plan 「{planName}」 has fallen below {ratio}%. As of now, you have used {used}, with a maximum limit of {limit}. Please pay attention to avoid any impact on future usage.',
    button: 'Upgrade Now',
  },
  ja: {
    subject: '重要: あなたのスペースステーション機能の使用量が{ratio}%を下回りました',
    template:
      '尊敬なるユーザー様、現在のサブスクリプションプラン「{planName}」における機能「{featureName}」の使用量が{ratio}%を下回っていることを検出しました。現在の使用量は{used}で、最大制限は{limit}です。今後の使用に影響を与えないようご注意ください。',
    button: '今すぐアップグレード',
  },
};

export default UsageWarningEmailI18n;
