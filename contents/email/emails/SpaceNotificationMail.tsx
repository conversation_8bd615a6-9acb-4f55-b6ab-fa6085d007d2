import { Button, Text } from '@react-email/components';
import * as React from 'react';
import { MailTemplateResult } from '@bika/types/notification/vo';
import { SpaceVO } from '@bika/types/space/vo';
import { iStringParse, Locale } from '@bika/types/system';
import { SpaceNotificationMailI18n } from './SpaceNotificationMail.i18n';
import { EmailLayout } from '../components/layout';

/**
 * 通用的 Space Notification 邮件模板
 */
export const SpaceNotificationMail = (
  params: {
    space: SpaceVO;
    title: string;
    content: string;
    url?: string;
    buttonText?: string;
  },
  locale?: Locale,
): MailTemplateResult => {
  const { space, title, content, url, buttonText } = params;

  const mailContent = (
    <EmailLayout title={title} space={space} preview={title} locale={locale}>
      <Text>{content}</Text>
      {url !== undefined && (
        <Button
          href={url}
          className="w-fit px-4 py-2 rounded-full bg-[#8355EC] text-[14px] text-center font-semibold my-10 block text-white"
        >
          {buttonText || iStringParse(SpaceNotificationMailI18n.default_button_text, locale)}
        </Button>
      )}
    </EmailLayout>
  );
  return { title, content: mailContent };
};
