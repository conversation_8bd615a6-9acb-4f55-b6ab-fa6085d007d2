import { Text } from '@react-email/components';
import _ from 'lodash';
import * as React from 'react';
import { BillingUsagePlanFeature, SpacePlanType } from '@bika/types/pricing/bo';
import { SpaceVO } from '@bika/types/space/vo';
import { formatBytes } from '@bika/types/utils';
import BaseEmail, { EmailProps } from './base-email';
import UsageWarningEmailI18n from './UsageWarningEmail.i18n';
import { SpacePlanDescConfig } from '../../config/server/pricing';
import { getUsagePlanFeatureName } from '../../config/server/pricing/feature-name';
import { getServerLocaleContext } from '../../i18n/server';
import { EmailLayout } from '../components/layout';

const { template } = _;

interface Props extends EmailProps {
  space: SpaceVO;
  plan: SpacePlanType;
  feature: BillingUsagePlanFeature;
  used: number;
  limit: number;
  ratio: number;
}

export class UsageWarningEmail extends BaseEmail<Props> {
  protected i18n(): { [k: string]: string } {
    return UsageWarningEmailI18n[this.props.locale];
  }

  protected title(): string {
    const { ratio } = this.props;
    const templateString = this.i18n().subject;
    const compiled = template(templateString.replace(/{/g, '<%= ').replace(/}/g, ' %>'));
    return compiled({ ratio: ratio * 100 });
  }

  subject(): string {
    const { ratio } = this.props;
    const templateString = this.i18n().subject;
    const compiled = template(templateString.replace(/{/g, '<%= ').replace(/}/g, ' %>'));
    return compiled({ ratio: ratio * 100 });
  }

  renderAsText(): string {
    const { locale, space, plan, feature, used, limit, ratio } = this.props;
    const { i } = getServerLocaleContext(locale);
    const i18nPlanName = SpacePlanDescConfig[plan].planName;
    const planName = i(i18nPlanName);
    const i18nFeatureName = getUsagePlanFeatureName(feature);
    const featureName = i(i18nFeatureName);
    const templateString = this.i18n().template;
    const compiled = template(templateString.replace(/{/g, '<%= ').replace(/}/g, ' %>'));
    return compiled({
      name: space.name,
      planName,
      featureName,
      limit: feature === 'STORAGES' ? formatBytes(limit) : limit,
      used: feature === 'STORAGES' ? formatBytes(used) : used,
      ratio: ratio * 100,
    });
  }

  render(): React.ReactElement {
    const { locale, space, plan, feature, used, limit, ratio } = this.props;
    const { i } = getServerLocaleContext(locale);
    const i18nPlanName = SpacePlanDescConfig[plan].planName;
    const planName = i(i18nPlanName);
    const i18nFeatureName = getUsagePlanFeatureName(feature);
    const featureName = i(i18nFeatureName);
    const templateString = this.i18n().template;
    const compiled = template(templateString.replace(/{/g, '<%= ').replace(/}/g, ' %>'));
    return (
      <EmailLayout title={this.title()} space={space} preview={this.subject()} locale={locale}>
        <Text>
          {compiled({
            name: space.name,
            planName,
            featureName,
            limit: feature === 'STORAGES' ? formatBytes(limit) : limit,
            used: feature === 'STORAGES' ? formatBytes(used) : used,
            ratio: ratio * 100,
          })}
        </Text>
      </EmailLayout>
    );
  }
}
