import { Text } from '@react-email/components';
import _ from 'lodash';
import * as React from 'react';
import { BillingUsagePlanFeature } from '@bika/types/pricing/bo';
import { SpaceVO } from '@bika/types/space/vo';
import { formatBytes } from '@bika/types/utils';
import BaseEmail, { EmailProps } from './base-email';
import UsageReminderEmailI18n from './UsageReminderEmail.i18n';
import { getUsagePlanFeatureName } from '../../config/server/pricing/feature-name';
import { getServerLocaleContext } from '../../i18n/server';
import { EmailLayout } from '../components/layout';

const { template } = _;

type Props = EmailProps & {
  space: SpaceVO;
  feature: BillingUsagePlanFeature;
  used: number;
  limit: number;
};

export class UsageReminderEmail extends BaseEmail<Props> {
  protected i18n(): { [k: string]: string } {
    return UsageReminderEmailI18n[this.props.locale];
  }

  protected title(): string {
    return this.i18n().subject;
  }

  subject(): string {
    return this.i18n().subject;
  }

  renderAsText(): string {
    const { locale, space, feature, used, limit } = this.props;
    const { i } = getServerLocaleContext(locale);
    const featureName = getUsagePlanFeatureName(feature);
    const templateString = this.i18n().template;
    const compiled = template(templateString.replace(/{/g, '<%= ').replace(/}/g, ' %>'));
    return compiled({
      name: space.name,
      feature: i(featureName),
      limit: feature === 'STORAGES' ? formatBytes(limit) : limit,
      used: feature === 'STORAGES' ? formatBytes(used) : used,
    });
  }

  render(): React.ReactElement {
    const { locale, space, feature, used, limit } = this.props;
    const { i } = getServerLocaleContext(locale);
    const featureName = getUsagePlanFeatureName(feature);
    const templateString = this.i18n().template;
    const compiled = template(templateString.replace(/{/g, '<%= ').replace(/}/g, ' %>'));
    return (
      <EmailLayout title={this.i18n().subject} space={space} preview={this.i18n().subject} locale={locale}>
        <Text>
          {compiled({
            name: space.name,
            feature: i(featureName),
            limit: feature === 'STORAGES' ? formatBytes(limit) : limit,
            used: feature === 'STORAGES' ? formatBytes(used) : used,
          })}
        </Text>
      </EmailLayout>
    );
  }
}
