import { getAppEnv, isInCI, isInUnitTest } from 'sharelib/app-env';
import type { PresetLanguageAIModelDef, AIImageModelDef } from '@bika/types/ai/bo';
import type { CustomAIProviderIntegration } from '@bika/types/integration/bo';

export function getDefaultAIModel(): PresetLanguageAIModelDef {
  if (getAppEnv() === 'LOCAL' || isInCI() || isInUnitTest()) {
    return 'qwen/qwen-plus'; // 'doubao'; // lite //
    // return 'qwen/qwen3-coder'; // 'doubao'; // lite //
    //   // return 'siliconflow/DeepSeek-R1-Distill-Qwen-7B';
    //   // return 'siliconflow/DeepSeek-R1';
  }
  return 'openai/gpt-4.1';
}

export type IAILanguageModelConfig = CustomAIProviderIntegration & {
  modelId: string;
  alternates?: IAILanguageModelConfig[];
};

export type IAIImageModelConfig = CustomAIProviderIntegration & {
  modelId: string;
  alternates?: IAIImageModelConfig[];
};

// export type IAIModelConfig = {
//   baseUrl: string;
//   modelId: string;
//   apiKey: string;
//   provider?: string;
//   secretAccessKey?: string;
// };
/** IAILanguageModelConfig
 * 配置AI Model和服务器的关系
 *
 * AI Model 定价，放在ai-models-price.xlsx
 */
export const PresetLanguageModelServerConfig: Record<PresetLanguageAIModelDef, IAILanguageModelConfig> = {
  // llama3: {
  //   baseUrl: 'https://ollama.bika.hk/v1',
  //   modelId: 'llama3',
  //   apiKey: 'NO_OPENAI_API_KEY_NEEDED',
  // },
  // phi3: {
  //   baseUrl: 'https://ollama.bika.hk/v1',
  //   modelId: 'phi3',
  //   apiKey: 'NO_OPENAI_API_KEY_NEEDED',
  // },
  // gemma: {
  //   baseUrl: 'https://ollama.bika.hk/v1',
  //   modelId: 'gemma',
  //   apiKey: 'NO_OPENAI_API_KEY_NEEDED',
  // },
  // mistral: {
  //   baseUrl: 'https://ollama.bika.hk/v1',
  //   modelId: 'mistral',
  //   apiKey: 'NO_OPENAI_API_KEY_NEEDED',
  // },
  // 'gpt-3.5': {
  //   baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
  //   modelId: 'gpt-3.5-turbo-0125',
  //   type: 'OPENAI',
  //   apiKey: process.env.OPENAI_API_KEY!,
  // },
  // 'gpt-4o-mini': {
  //   baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
  //   modelId: 'gpt-4o-mini-2024-07-18',
  //   type: 'OPENAI',
  //   apiKey: process.env.OPENAI_API_KEY!,
  // },
  // 'gpt-4.1': ,
  // 'gpt-4.1-mini': ,
  // 'gpt-image-1': {
  //   baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
  //   modelId: 'gpt-image-1',
  //   type: 'OPENAI',
  //   apiKey: process.env.OPENAI_API_KEY!,
  // },
  'openai/gpt-4o': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-4o',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    alternates: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4o-2024-08-06',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/gpt-4o-mini': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-4o-mini',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    alternates: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-mini-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/gpt-4.1': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-4.1',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    alternates: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/gpt-4.1-mini': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-4.1-mini',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    alternates: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-mini-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/o4-mini': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'o4-mini',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    alternates: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-mini-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/o3': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'o3-mini',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    alternates: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-mini-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  // 'bytedance/doubao': {
  //   type: 'OPENAI',
  //   baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
  //   modelId: 'doubao-1-5-lite-32k-250115',
  //   apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  // },
  'bytedance/doubao-pro-32k': {
    type: 'DEEPSEEK',
    baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
    modelId: 'doubao-1-5-pro-32k-250115', // 32k
    apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  },
  'bytedance/doubao-pro-256k': {
    type: 'DEEPSEEK',
    baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
    modelId: 'doubao-1-5-pro-256k-250115',
    apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  },
  'deepseek/deepseek-r1': {
    type: 'DEEPSEEK',
    baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
    modelId: 'deepseek-r1-250528',
    apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  },
  'deepseek/deepseek-v3': {
    type: 'DEEPSEEK',
    baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
    modelId: 'deepseek-v3-250324',
    apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  },
  'qwen/qwen3-coder': {
    type: 'DEEPSEEK',
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    modelId: 'qwen3-coder-plus', // 'qwen3
    apiKey: 'sk-193e96c2115a410ba45674c5c5b3af59',
  },
  'qwen/qwen-plus': {
    type: 'DEEPSEEK',
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    modelId: 'qwen-plus-latest', // 'qwen-plus-2025-04-28', // 'qwen3-235b-a22b',
    apiKey: 'sk-193e96c2115a410ba45674c5c5b3af59',
  },
  'qwen/qwen-turbo': {
    type: 'DEEPSEEK',
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    modelId: 'qwen-turbo-latest', // 'qwen-turbo-2025-04-28', // 'qwen3-235b-a22b',
    apiKey: 'sk-193e96c2115a410ba45674c5c5b3af59',
  },
  // 'siliconflow/DeepSeek-V3': {
  //   type: 'OPENAI',
  //   baseUrl: 'https://api.siliconflow.cn/v1',
  //   modelId: 'deepseek-ai/DeepSeek-V3',
  //   apiKey: 'sk-kkdxtpugigfiyuddlqvobfujegssmcwypwjifijdbjtbytmm',
  // },
  // 'siliconflow/DeepSeek-R1': {
  //   type: 'OPENAI',
  //   baseUrl: 'https://api.siliconflow.cn/v1',
  //   modelId: 'deepseek-ai/DeepSeek-R1',
  //   apiKey: 'sk-kkdxtpugigfiyuddlqvobfujegssmcwypwjifijdbjtbytmm',
  // },
  // 'siliconflow/DeepSeek-R1-Distill-Qwen-7B': {
  //   type: 'OPENAI',
  //   baseUrl: 'https://api.siliconflow.cn/v1',
  //   modelId: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
  //   apiKey: 'sk-kkdxtpugigfiyuddlqvobfujegssmcwypwjifijdbjtbytmm',
  // },
  'google/gemini-2.5-pro': {
    type: 'GOOGLE_AI',
    modelId: 'gemini-2.0-flash-exp',
    apiKey: process.env.GOOGLE_GENAI_API_KEY || 'AIzaSyA_mYJ_8eMvY0s1-4mwTNUDl59XKmW-f9k',
  },
  'google/gemini-2.5-flash': {
    type: 'GOOGLE_AI',
    modelId: 'gemini-2.0-flash-exp',
    apiKey: process.env.GOOGLE_GENAI_API_KEY || 'AIzaSyA_mYJ_8eMvY0s1-4mwTNUDl59XKmW-f9k',
  },
  // gptproto: {
  //   type: 'OPENAI',
  //   baseUrl: 'https://api.gptproto.com/v1',
  //   apiKey: 'sk-d09d0b64ed074f1aa3986d3660d54fe7',
  //   modelId: 'gpt-4o',
  // },
  // 'anthropic/claude-sonnet': {
  //   type: 'AMAZON_BEDROCK',
  //   baseUrl: process.env.AWS_BEDROCK_BASE_URL || 'https://bedrock.bika.ltd',
  //   modelId: 'us.anthropic.claude-3-sonnet-20240229-v1:0',
  //   apiKey: process.env.AWS_ACCESS_KEY_ID!,
  //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  // },
  'anthropic/claude-sonnet-3.7': {
    type: 'AMAZON_BEDROCK',
    baseUrl: process.env.AWS_BEDROCK_BASE_URL || 'https://bedrock.bika.ltd',
    modelId: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
    apiKey: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
  'anthropic/claude-opus-4': {
    type: 'AMAZON_BEDROCK',
    baseUrl: process.env.AWS_BEDROCK_BASE_URL || 'https://bedrock.bika.ltd',
    modelId: 'us.anthropic.claude-opus-4-20250514-v1:0',
    apiKey: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
  'anthropic/claude-sonnet-4': {
    type: 'AMAZON_BEDROCK',
    baseUrl: process.env.AWS_BEDROCK_BASE_URL || 'https://bedrock.bika.ltd',
    modelId: 'us.anthropic.claude-sonnet-4-20250514-v1:0',
    apiKey: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
  mock: {
    type: 'MOCK',
    modelId: 'mock',
    // baseUrl: '',
    // modelId: '',
    // apiKey: '',
    // provider: undefined,
    // secretAccessKey: undefined,
  },
  'bytedance/doubao-lite-128k': {
    type: 'MOCK',
    modelId: 'mock',
  },
  'qwen/qwen-pro': {
    type: 'MOCK',
    modelId: 'mock',
  },
  'anthropic/claude-haiku-3.5': {
    type: 'MOCK',
    modelId: 'mock',
  },
  'openai/gpt-5-mini': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-5-mini',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    alternates: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-5-mini',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/gpt-5': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-5',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    alternates: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-5',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
};

export const PresetImageModelsServerConfig: Record<AIImageModelDef, IAIImageModelConfig> = {
  'openai/gpt-image-1': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-image-1',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    alternates: [
      {
        type: 'OPENAI',
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-image-1',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/dall-e-3': {
    type: 'OPENAI',
    baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
    modelId: 'dall-e-3',
    apiKey: process.env.OPENAI_API_KEY!,
  },
  'flux/flux-kontext-pro': {
    type: 'OPENAI',
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    modelId: 'flux-kontext-pro',
    apiKey: 'sk-193e96c2115a410ba45674c5c5b3af59',
  },
};
