import type { IAutomationExample } from '../automation/example';

export const DatabaseCreateExamples: IAutomationExample[] = [
  {
    templateId: 'beginner-playground',
    resourceIndex: 4,
  },
  {
    templateId: 'crm-b2b-sales',
    resourceIndex: 2,
  },
  {
    templateId: '@vika/scrum-standup',
    resourceIndex: 1,
  },
  {
    templateId: 'ai-automated-ticket-system',
    resourceIndex: 1,
  },
  {
    templateId: 'rotating-duty-reminder-wecom',
    resourceIndex: 0,
  },
  {
    templateId: 'assortment-planning',
    resourceIndex: 0,
  },
  {
    templateId: 'assortment-planning',
    resourceIndex: 1,
  },
  {
    templateId: 'x-ai-automated-tweets',
    resourceIndex: 1,
  },
  {
    templateId: 'invoice-org-ai-automated-remind',
    resourceIndex: 1,
  },
  {
    templateId: 'send-emails-in-bulk',
    resourceIndex: 0,
  },
];
