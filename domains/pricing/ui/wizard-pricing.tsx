import Link from 'next/link';
import { match } from 'ts-pattern';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { Button } from '@bika/ui/button';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import { PopularTip } from './components/popular-tip/popular-tip';

interface Props {
  onClick: () => Promise<void>;
}

export const WizardPricing = (props: Props) => {
  const trpcQuery = useTRPCQuery();
  const { t, lang } = useLocale();

  const { data: pricingConfig } = trpcQuery.pricing.getPricing.useQuery({ lang });

  const plans = pricingConfig?.filter((item) => {
    // TODO: 暂时只保留free
    if (item.plan === 'FREE') return true;
    return false;
  });

  return (
    <div className={'space-x-8 flex items-stretch'}>
      {plans?.map((item) => {
        const manualPrice = item.prices.find((price) => price.interval === 'year')!;
        return (
          <div
            key={item.plan}
            className={'w-1/2 flex flex-col p-4'}
            style={{
              background: 'var(--bg-controls)',
              borderRadius: '8px',
            }}
          >
            <div className={'text-h6 flex items-center'}>
              {item.plan}
              {Boolean(manualPrice.amount) && <PopularTip />}
            </div>
            <div className={'flex items-end text-b2'}>
              <span>{manualPrice.currency}</span>
              {Boolean(manualPrice.amount) && <span className={'line-through'}>{manualPrice.amount}</span>}
              <span className={'text-h3 ml-1 leading-[1.2]'}>0</span>
              {Boolean(manualPrice.amount) && <span className={'text-[--text-secondary]'}> /mon per member</span>}
            </div>
            {Boolean(manualPrice.amount) && (
              <div className={'text-b4 text-[--text-secondary]'}>
                {t('pricing.free_trial_7_days', { price: manualPrice.currency })}
                {manualPrice.amount}
              </div>
            )}
            <div className={'space-y-2 mt-4 flex-1'}>
              {item.highlights.map((highlight, index) => (
                <div key={index} className={'flex items-center space-x-1 '}>
                  <span>{<CheckOutlined color={'#907FF0'} size={16} />}</span>
                  <span>{highlight}</span>
                </div>
              ))}
            </div>
            {Boolean(manualPrice.amount) && (
              <div className={'text-b4 text-[--text-secondary] mt-4'}>
                {t.pricing.renew_and_cancel}{' '}
                <Link
                  href={{
                    pathname: `/${lang}/pricing`,
                  }}
                  target="_blank"
                >
                  {t.pricing.view_detail}
                </Link>
              </div>
            )}
            <div className={'mt-4'}>
              {match(manualPrice.amount)
                .with(0, () => <Button onClick={props.onClick}>{t.settings.coin_rewards.choose_plan}</Button>)
                .otherwise(() => (
                  <Button onClick={props.onClick}>Subscribe with trial</Button>
                ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};
