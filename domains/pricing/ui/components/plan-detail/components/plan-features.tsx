import React, { useMemo } from 'react';
import { match } from 'ts-pattern';
import { featuresGroupFormatterConfigs } from '@bika/contents/config/server/pricing/features-group';
import { useLocale } from '@bika/contents/i18n/context';
import {
  BillingPlanFeature,
  BillingPlanFeatureConfig,
  BillingPlanFeatureKeyMapper,
  BillingPlanFeatures,
} from '@bika/types/pricing/bo';
import { PricingItem } from '@bika/types/pricing/vo';
import { iStringParse } from '@bika/types/system';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import QuestionCircleOutlined from '@bika/ui/icons/components/question_circle_outlined';
import { Tooltip } from '@bika/ui/tooltip';

// const { mapValues } = _;

interface Props {
  pricingItems: PricingItem[];
}

export const PlanFeatures: React.FC<Props> = ({ pricingItems }) => {
  const locale = useLocale();
  const { t } = locale;

  const features: Record<BillingPlanFeature, (string | number | boolean | null)[]> = useMemo(() => {
    const featureCollect: BillingPlanFeatureConfig[] = pricingItems.map((pricingItem) => pricingItem.features);
    const featureMap: Record<BillingPlanFeature, (string | number | boolean | null)[]> = BillingPlanFeatures.reduce(
      (acc, key) => {
        const planFeature = BillingPlanFeatureKeyMapper[key];
        const values: (string | number | boolean | null)[] = [];
        featureCollect.forEach((feature) => {
          values.push(feature[planFeature]);
        });
        return { ...acc, [key]: values };
      },
      {} as Record<BillingPlanFeature, (string | number | boolean | null)[]>,
    );
    return featureMap;
  }, [pricingItems]);

  const formatConfigs = useMemo(() => featuresGroupFormatterConfigs(locale), [locale]);

  return (
    <div className={'w-full'}>
      {formatConfigs.map((group, idx) => (
        <div key={idx} className={'mt-6 first:mt-0'}>
          <p
            className={
              'text-[20px] font-bold text-[--text-primary] text-opacity-[0.7] border-b-[1px] py-4 border-[--border-default] pl-[40px] mt-10 first:mt-0'
            }
          >
            {iStringParse(group.groupName, locale.lang)}
          </p>
          {group.features
            .filter((feature) => !feature.hidden)
            .map((item, index) => (
              <div
                className={'flex items-center border-b-[1px] py-4 border-[--border-default]  space-x-10'}
                key={index}
              >
                <div
                  className={
                    'text-[--text-secondary] text-opacity-[0.7] text-[13px]  flex-1 flex items-center space-x-2'
                  }
                >
                  <div className={'pl-[40px]'}>{item.name}</div>
                  {item.tips && (
                    <Tooltip
                      title={item.tips}
                      placement="top"
                      arrow
                      // placement={'top'}
                      // trigger={'hover'}
                      // className={'cursor-pointer'}
                    >
                      <span className={'cursor-pointer'}>
                        <QuestionCircleOutlined color={'var(--text-primary)'} />
                      </span>
                    </Tooltip>
                  )}
                </div>
                {features[item.key]?.map((detail, i) => (
                  <div className={'text-[13px] text-[--text-primary] text-center  flex justify-center flex-1'} key={i}>
                    {match(detail)
                      .with(-1, () => t.pricing.features.unlimited)
                      .with(0, () => <CloseOutlined color={'#C96562'} />)
                      .with(true, () => <CheckOutlined color={'#907FF0'} />)
                      .with(false, () => <CloseOutlined color={'#C96562'} />)
                      .with('YES', () => <CheckOutlined color={'#907FF0'} />)
                      .with('NO', () => <CloseOutlined color={'#C96562'} />)
                      .with('COMING_SOON', () => t.pricing.features.coming_soon)
                      .with(-2, () => 'Customize')
                      .with(null, () => 'Customize')
                      .otherwise(() => {
                        if (item.customFormat) {
                          if (typeof detail === 'string' || typeof detail === 'number') {
                            return item.customFormat(detail);
                          }
                          // return empty string
                          return '';
                        }
                        return typeof detail === 'number' ? detail.toLocaleString('en-us') : detail;
                      })}
                  </div>
                ))}
              </div>
            ))}
        </div>
      ))}
    </div>
  );
};
