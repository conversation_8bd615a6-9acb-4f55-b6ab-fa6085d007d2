import dayjs from 'dayjs';
import { BillingPlanFeatureMap, UsageType, UsageTypes } from '@bika/types/pricing/bo';
import { UsageVO } from '@bika/types/pricing/vo';
import { UsageCheck } from './types';
import { getUsageCounter } from './usage-counter';
import { UsageStatSO } from './usage-stat-so';
import { PlanFeatureSO } from '../plan-feature-so';
import { SubscriptionSO } from '../subscription';

interface PeriodDateRange {
  begin: Date;
  end: Date;
}

/**
 * 用量服务对象，可以得到一个空间的用量, 可以检查/限制
 * Tips: UsageType 只会记录需要计量的类型,并不是计划里的所有数字类型规格(比如不在计量范围的规格类型: 自动化运行历史最多查看天数)
 */
export class UsageSO {
  private readonly _spaceId: string;

  private readonly _subscription: SubscriptionSO | null;

  private readonly _planFeature: PlanFeatureSO;

  constructor(spaceId: string, subscription: SubscriptionSO | null, planFeature: PlanFeatureSO) {
    this._spaceId = spaceId;
    this._subscription = subscription;
    this._planFeature = planFeature;
  }

  get plan(): string {
    return this._planFeature.plan;
  }

  /**
   * 当前订阅对应的周期范围
   * 免费版: 无订阅, 取当前月
   * 付费版: 取订阅的周期范围
   */
  private get planPeriodDateRange(): PeriodDateRange {
    if (!this._subscription) {
      // 免费版
      const begin = dayjs().startOf('month').toDate();
      const end = dayjs().endOf('month').toDate();
      return { begin, end };
    }
    return { begin: this._subscription.periodStartDate, end: this._subscription.periodEndDate };
  }

  private get planFeature(): BillingPlanFeatureMap {
    return this._planFeature.featureMap;
  }

  /**
   * 获取最大用量配置值, 有可能没配置
   * @param type 用量类型
   * @returns 最大用量值
   */
  getMaxUsageValue(type: UsageType): number | undefined {
    const value = this.planFeature[type];
    if (type === 'STORAGES' && value && value > 0) {
      return value * 1024 * 1024 * 1024; // GB to Byte
    }
    return value;
  }

  /**
   * 获取功能已使用的用量值
   * 不适用 单表记录数
   */
  async getUsedUsageValue(type: UsageType): Promise<number> {
    const counter = getUsageCounter(this._spaceId, type);
    return counter.getUsedUsage({ dateRange: this.planPeriodDateRange });
  }

  /**
   * 是否可以使用增加用量
   * @param type usage type
   * @param value to add number
   * @returns true if can add
   */
  async can(condition: { type: UsageType; value: number }): Promise<UsageCheck> {
    const { type, value } = condition;
    // 判断用量类型去查找用量类型对应计算器, 有些用量类型是没有计算器的, 需要特殊条件才能满足检查,比如单表记录数
    const counter = getUsageCounter(this._spaceId, type);
    const used = await counter.getUsedUsage({ dateRange: this.planPeriodDateRange });
    const max = this.getMaxUsageValue(type);
    if (!max) {
      // 没有配置
      return { exceed: false, used, limit: 0 };
    }
    const exceed = used + value > max;
    if (!exceed) {
      return { exceed: false, used, limit: max };
    }
    return { exceed: true, used, limit: max };
  }

  async getStats(): Promise<UsageStatSO[]> {
    const { begin, end } = this.planPeriodDateRange;
    return UsageStatSO.findByDateRange(this._spaceId, 'MONTH', begin, end);
  }

  async getUsedUsageCount(): Promise<Record<UsageType, number>> {
    const counters = UsageTypes.map((type) => getUsageCounter(this._spaceId, type));
    const entries = await Promise.all(
      counters.map(async (counter) => {
        const used = await counter.getUsedUsage({ dateRange: this.planPeriodDateRange });
        return [counter.getUsageType(), used];
      }),
    );
    return Object.fromEntries(entries);
  }

  async toVO(): Promise<UsageVO[]> {
    const usedUsage = await this.getUsedUsageCount();
    return Promise.all(
      UsageTypes.map(async (type) => {
        const maxValue = this.getMaxUsageValue(type);
        const max = maxValue ?? 0;
        let current = 0;
        if (type in usedUsage) {
          const used = usedUsage[type];
          if (used) {
            current = used;
          }
        }
        return { type, current, max };
      }),
    );
  }
}
