'use client';

import { useSearchParams } from 'next/navigation';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { Pricing } from '@bika/domains/pricing/ui';
import type { PricingGroup, PayInterval } from '@bika/types/pricing/vo';
import { Skeleton } from '@bika/ui/skeleton';

type Props = {
  type?: 'setting' | 'normal';
};

export function PricingModal({ type = 'normal' }: Props) {
  const trpcQuery = useTRPCQuery();
  const searchParams = useSearchParams();
  const { lang } = useLocale();

  const pricingGroup = (searchParams.get('group') || 'FOR_INDIVIDUALS_AND_TEAMS').toUpperCase() as PricingGroup;
  const payInterval = searchParams.get('period') as PayInterval;
  const { data: allPricingItems } = trpcQuery.pricing.getPricing.useQuery({ lang });

  if (!allPricingItems) return <Skeleton pos="PRICING_MODAL" />;

  const pricingItems = allPricingItems.filter((item) => item.group === pricingGroup);

  return (
    <Pricing
      // locale={lang}
      pricingGroup={pricingGroup}
      payInterval={payInterval}
      pricingItems={pricingItems}
      type={type}
    />
  );
}
