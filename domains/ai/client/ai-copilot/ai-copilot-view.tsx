import assert from 'assert';
import { useSearchParams } from 'next/navigation';
import { useEffect, useRef } from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n';
import type { SpaceUICopilot } from '@bika/types/space/bo';
import { useSpaceId, useSpaceContext, useSpaceContextForce, useSpaceRouter } from '@bika/types/space/context';
import { IconButton } from '@bika/ui/button';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import HistoryOutlined from '@bika/ui/icons/components/history_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { snackbarShow } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip-components';
import { AIChatHandle } from '../chat/ai-chat-ui';
import { AIChatView } from '../chat/ai-chat-view';
import { useAIChatSession, type AIChatSelector } from '../chat/hooks/use-ai-chat-cache';

interface Props {
  copilotState: SpaceUICopilot;
}

function AICopilotViewInternal(props: Props) {
  const spaceContext = useSpaceContext();
  const { copilotState } = props;
  assert(copilotState, 'AICopilotView requires copilotState to be set in space context');

  const { t } = useLocale();
  const { useRootNode } = useSpaceContextForce();
  const { findNode } = useRootNode();
  const selector: AIChatSelector = { type: 'copilot', spaceId: spaceContext!.data.id, copilot: copilotState };
  const { session, isLoading: isLoadingCache } = useAIChatSession(selector);

  const router = useSpaceRouter();

  const searchParams = useSearchParams();

  useEffect(() => {
    const paramWizardId = searchParams.get('chatId');
    if (paramWizardId === session.chatId) {
      return;
    }
    if (paramWizardId && copilotState) {
      session.setChatId(paramWizardId);
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.delete('chatId');
      router.replace(`${window.location.pathname}?${newSearchParams.toString()}`);
    }
  }, [searchParams, copilotState]);

  const { trpc, trpcQuery } = useApiCaller();
  useEffect(() => {
    // 从设置中跳转到 Copilot
    const modifyHTMLParam = searchParams.get('ModifyHTML');
    if (modifyHTMLParam !== null) {
      // cacheChatId(Date.now());
      // 清除 URL 参数
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.delete('ModifyHTML');
      router.replace(`${window.location.pathname}?${newSearchParams.toString()}`);
    }
  }, [searchParams, router]);

  const spaceId = useSpaceId();
  const createNewWizard = trpcQuery.ai.newWizard.useMutation();

  const chatUIHandle = useRef<
    AIChatHandle & {
      reset: () => void;
    }
  >(null);
  if (isLoadingCache) {
    return <>Loaindg Cache...</>; // 或者显示一个加载状态
  }

  assert(copilotState.type === 'node', 'Copilot must be a node type');
  const node = findNode(copilotState.nodeId);

  return (
    <Box sx={{ overflow: 'hidden', width: '100%', height: '100%', position: 'relative' }}>
      <Box
        px={2}
        height="48px"
        justifyContent="space-between"
        alignItems="center"
        display="flex"
        borderBottom="1px solid var(--border-default)"
      >
        <Typography textColor={'var(--text-primary)'} level="h6">
          {t.global.copilot.title}
        </Typography>
        <Stack direction="row" gap={1}>
          <Tooltip title={t.global.copilot.new_chat}>
            <IconButton
              onClick={async () => {
                const data = await createNewWizard.mutateAsync({
                  spaceId: spaceId!,
                  intent: {
                    type: 'COPILOT',
                    copilot: copilotState,
                  },
                });
                snackbarShow({
                  content: t.wizard.new_wizard_created,
                  color: 'success',
                });
                session.setChatId(data.id);
                chatUIHandle.current?.reset();
              }}
            >
              <AddOutlined color="var(--text-primary)" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t.global.copilot.history}>
            <IconButton
              onClick={(e) => {
                e.preventDefault();
                spaceContext?.showUIDrawer({ type: 'ai-history', props: { type: 'AI_COPILOT' } });
              }}
            >
              <HistoryOutlined color="var(--text-primary)" />
            </IconButton>
          </Tooltip>
          <IconButton
            onClick={(e) => {
              e.preventDefault();
              spaceContext?.showAICopilot(null);
            }}
          >
            <CloseOutlined color="var(--text-primary)" />
          </IconButton>
        </Stack>
      </Box>
      <Box
        sx={{
          height: 'calc(100% - 48px)',
        }}
      >
        <AIChatView
          inputState={session}
          displayMode={'COPILOT'}
          initAIIntent={{
            type: 'COPILOT',
            copilot: copilotState,
          }}
          ref={chatUIHandle}
          config={{
            contexts: [
              {
                type: 'node',
                node: node!,
                // nodeId: copilotState.nodeId,
              },
            ],
            allowContextMenu: ['attachment'],
          }}
          // context={[
          //   {
          //     label: (
          //       <div className="flex items-center gap-1 px-[16px] py-[8px] text-[--text-disabled] space-x-2 border border-[--border-default] rounded-lg h-[32px] cursor-pointer">
          //         {node?.type && (
          //           <NodeIcon
          //             value={{ kind: 'node-resource', nodeType: node?.type }}
          //             size={16}
          //             color="var(--text-disabled)"
          //           />
          //         )}
          //         {/* <NodeIcon type={node?.type} size={16} color="var(--text-disabled)" /> */}

          //         <div className="text-b3">{node?.name}</div>
          //       </div>
          //     ),
          //     value: copilotState.nodeId,
          //     type: 'NODE',
          //     fixed: true,
          //   },
          // ]}
        />
      </Box>
    </Box>
  );
}

export function AICopilotView() {
  const spaceContext = useSpaceContext();
  const copilotState = spaceContext?.getAICopilot();
  if (!copilotState) {
    return null;
  }

  return <AICopilotViewInternal copilotState={copilotState} />;
}
