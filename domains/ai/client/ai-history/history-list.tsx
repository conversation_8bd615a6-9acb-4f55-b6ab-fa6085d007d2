import {
  useFloating,
  autoUpdate,
  useClick,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
  FloatingFocusManager,
  offset,
  flip,
  shift,
} from '@floating-ui/react';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, Fragment, useState, useMemo } from 'react';
import { useApiCaller, keepPreviousData } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n';
import type { AIWizardDTO } from '@bika/types/ai/dto';
import type { AIWizardSimpleVO } from '@bika/types/ai/vo';
import { useSpaceId, useSpaceRouter, useSpaceContextForce } from '@bika/types/space/context';
import { useGlobalContext } from '@bika/types/website/context';
import { IconButton } from '@bika/ui/button-component';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import MoreOutlined from '@bika/ui/icons/components/more_outlined';
import RestoreOutlined from '@bika/ui/icons/components/restore_outlined';
import ShareOutlined from '@bika/ui/icons/components/share_outlined';
import { Modal } from '@bika/ui/modal';
import { Skeleton } from '@bika/ui/skeleton';
import { type AIChatSelector, useAIChatSession } from '../chat/hooks/use-ai-chat-cache';

interface IProps {
  dto?: AIWizardDTO;
  layout?: 'column' | 'row'; // 用于控制布局方向
}

export const AIHistoryList = ({ dto, layout = 'column' }: IProps) => {
  const spaceId = useSpaceId();
  const router = useRouter();
  const { useParams } = useSpaceRouter();
  const { type = dto?.type || 'AI_COPILOT' } = dto || {};
  const paramsNodeId = useParams<{ nodeId: string }>().nodeId;
  const nodeId = paramsNodeId || (dto?.type === 'AI_NODE' ? dto?.nodeId : undefined);
  const { trpcQuery } = useApiCaller();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const spaceContext = useSpaceContextForce();

  const deleteWizard = trpcQuery.ai.deleteWizard.useMutation();

  const isRowLayout = layout === 'row';

  const { cacheSelector, link } = useMemo((): {
    cacheSelector: AIChatSelector;
    link: string;
  } => {
    if (type === 'AI_NODE') {
      return {
        cacheSelector: { type: 'agent', spaceId, agent: { type: 'node', nodeId: nodeId || '' } },
        link: `/space/${spaceId}/node/${nodeId}`,
      };
    }
    if (type === 'AI_BUILDER') {
      return {
        cacheSelector: { type: 'agent', spaceId, agent: { type: 'expert', expertKey: 'builder' } },
        link: `/space/${spaceId}/ai-app-builder`,
      };
    }
    if (type === 'AI_SUPERVISOR') {
      return {
        cacheSelector: { type: 'agent', spaceId, agent: { type: 'expert', expertKey: 'supervisor' } },
        link: `/space/${spaceId}/supervisor`,
      };
    }
    return {
      cacheSelector: { type: 'copilot', spaceId, copilot: { type: 'node', nodeId: type || '' } },
      link: `/space/${spaceId}/node/${type || ''}`,
    };
  }, [type, nodeId, spaceId]);

  const { session, isLoading: isLoadingSession } = useAIChatSession(cacheSelector);

  const [wizards, setWizards] = useState<AIWizardSimpleVO[]>([]);

  const [pageNo, setPageNo] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const pageSize = isRowLayout ? 12 : 10;

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [selectedWizard, setSelectedWizard] = useState<AIWizardSimpleVO | null>(null);
  const [selectedWizardIndex, setSelectedWizardIndex] = useState<number>(-1);

  const { i } = useLocale();

  const { refs, floatingStyles, context } = useFloating({
    open: isMenuOpen,
    onOpenChange: setIsMenuOpen,
    middleware: [offset(4), flip(), shift({ padding: 8 })],
    whileElementsMounted: autoUpdate,
    strategy: 'fixed',
    placement: 'bottom-start',
  });

  const click = useClick(context);
  const dismiss = useDismiss(context);
  const role = useRole(context);
  const globalContext = useGlobalContext();

  const { getFloatingProps } = useInteractions([click, dismiss, role]);
  const userTimezone = globalContext.authContext.me?.user.timeZone || globalContext.timezone;

  const virtualElement = {
    getBoundingClientRect() {
      return {
        width: 0,
        height: 0,
        x: mousePosition.x,
        y: mousePosition.y,
        left: mousePosition.x,
        top: mousePosition.y,
        right: mousePosition.x,
        bottom: mousePosition.y,
      };
    },
  };

  useEffect(() => {
    if (isMenuOpen) {
      refs.setReference(virtualElement);
    }
  }, [isMenuOpen, mousePosition.x, mousePosition.y]);

  const {
    data,
    isLoading: queryIsLoading,
    isFetching: queryIsFetching,
    isSuccess: querySuccess,
    isError: queryError,
    refetch,
  } = trpcQuery.ai.listWizard.useQuery(
    {
      pageNo,
      pageSize,
      type,
      nodeId,
      spaceId,
    },
    {
      placeholderData: keepPreviousData,
    },
  );

  useEffect(() => {
    if (queryError) {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [queryError]);

  useEffect(() => {
    if (querySuccess) {
      if (pageNo === 1) {
        setWizards(data.data);
      } else {
        setWizards((prevWizards) => [...prevWizards, ...data.data]);
      }
      const { pageNo: currentPage, pageSize: _, total } = data.pagination;
      setHasMore(data.data.length >= pageSize && currentPage * pageSize < total);
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [querySuccess, data, pageNo, pageSize]);

  useEffect(() => {
    if (pageNo > 1 && queryIsFetching) {
      setIsLoadingMore(true);
    } else if (!queryIsFetching) {
      setIsLoadingMore(false);
    }
    if (pageNo === 1 && queryIsLoading) {
      setIsLoading(true);
    } else if (!queryIsLoading && pageNo === 1) {
      setIsLoading(false);
    }
  }, [queryIsFetching, queryIsLoading, pageNo]);

  const loadMore = () => {
    if (hasMore && !isLoadingMore) {
      setPageNo((prevPageNo) => prevPageNo + 1);
    }
  };

  // const clearCacheChatId = (deleteId: string) => {
  //   const cacheId = localStorage.getItem(`ai-page-${nodeId}`);

  //   if (cacheId === deleteId) {
  //     localStorage.removeItem(`ai-page-${nodeId}`);
  //   }
  // };

  const isEmpty = !isLoading && wizards.length === 0;

  const { t } = useLocale();

  if (isLoadingSession) {
    // return <>Loading Session</>;
    return <Skeleton pos="NODE_PAGE" />;
  }

  const handleRestoreHistory = (wizardId: string) => {
    if (paramsNodeId) {
      const url = new URL(window.location.href);
      url.searchParams.delete('drawer');
      url.searchParams.set('chatId', wizardId);
      window.history.pushState(null, '', url.toString());
      setIsMenuOpen(false);
    } else {
      router.push(`${link}?chatId=${wizardId}`);
    }
  };
  const onDeleteWizard = (wizardId: string) => {
    deleteWizard.mutate(
      { wizardId },
      {
        onSuccess: () => {
          session.clear();
          refetch();
          session.setChatId(wizardId);
        },
      },
    );
  };

  const renderContent = () => {
    if (isLoading && pageNo === 1) {
      return (
        <div className="flex items-center justify-center h-full text-b3 text-[--text-secondary]">
          {t.global.copilot.history_loading}
        </div>
      );
    }
    if (isEmpty) {
      return (
        <div className="flex flex-col items-center justify-center space-y-4 h-full">
          <img src="/assets/placeholders/audit-dark.png" alt="ai_history_empty" width={160} height={160} />
          <div className="text-[--text-primary] text-b2">{t.global.copilot.history_empty}</div>
        </div>
      );
    }
    return (
      <div className={`flex ${isRowLayout ? 'flex-row' : 'flex-col'} flex-wrap gap-4 p-4`}>
        {wizards.map((wizard, index) => (
          <Fragment key={wizard.id}>
            <div
              className={classNames(
                'border-[--border-default] border rounded-[8px] p-[12px] bg-[--bg-controls] hover:bg-[--bg-controls-hover] hover:cursor-pointer',
                {
                  'w-[calc(33.33%-9px)]': isRowLayout,
                  'w-full': !isRowLayout,
                },
              )}
              style={{ height: '104px' }}
              onClick={() => {
                handleRestoreHistory(wizard.id);
              }}
            >
              <div className="flex items-center justify-between space-x-2">
                {/* <div className="flex items-center space-x-2 bg-[--bgTagDefault] rounded-full p-[2px] !pr-[4px]">
                  <AvatarImg name={'No Name'} customSize={AvatarSize.Size20} shape="CIRCLE" />
                  <span className="text-b4 text-[--textCommonPrimary]">No Name</span>
                </div> */}
                <div className="flex-1 text-h8 text-[--text-primary] truncate">
                  {i(wizard.title) || t.global.copilot.history_no_title}
                </div>
                <div className="text-b3 text-[--text-secondary] whitespace-nowrap">
                  {dayjs(wizard.createdAt).tz(userTimezone).format('YYYY-MM-DD HH:mm')}
                </div>
                <IconButton
                  onClick={(e) => {
                    e.stopPropagation();
                    setMousePosition({ x: e.clientX, y: e.clientY });
                    setSelectedWizard(wizard);
                    setSelectedWizardIndex(index);
                    setIsMenuOpen(true);
                  }}
                >
                  <MoreOutlined color={'var(--text-secondary)'} />
                </IconButton>
              </div>
              {wizard.description && (
                <div className="text-b3 text-[--text-secondary] line-clamp-2 mt-1">{wizard.description}</div>
              )}
            </div>
          </Fragment>
        ))}
        {isLoadingMore && (
          <div className={classNames('text-center text-b3 text-[--text-secondary] py-4', { 'w-full': isRowLayout })}>
            {t.global.copilot.history_loading}
          </div>
        )}
        {hasMore && !isLoadingMore && (
          <div className={classNames('flex justify-center py-4', { 'w-full': isRowLayout })}>
            <button
              onClick={loadMore}
              className="px-4 py-2 bg-[--bg-brand] text-[--text-white] rounded-lg hover:bg-[--bg-brand-hover] transition-colors"
            >
              {t.ai.load_more}
            </button>
          </div>
        )}
        {!hasMore && !isLoadingMore && wizards.length > 0 && (
          <div className={classNames('text-center text-b3 text-[--text-secondary]', { 'w-full': isRowLayout })}>
            {t.global.copilot.history_no_more}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div
        ref={scrollContainerRef}
        className={classNames('h-full overflow-y-auto', {
          'bg-[--bg-page]': !isRowLayout,
        })}
      >
        {renderContent()}
      </div>
      {isMenuOpen && selectedWizard && (
        <FloatingPortal>
          <FloatingFocusManager context={context} modal={false}>
            <div
              ref={refs.setFloating}
              style={{
                ...floatingStyles,
                margin: 0,
                boxShadow: '0 0 10px rgba(0,0,0,0.1)',
                padding: '8px',
                zIndex: 10000,
                border: '1px solid var(--border-default)', // 新增的边框样式
              }}
              className="w-[240px] bg-[--bg-popup] rounded-lg p-2 shadow-[--shadow-high]"
              {...getFloatingProps()}
            >
              <div
                className="py-2 px-4 cursor-pointer hover:bg-[--hover] flex items-center rounded-[4px]"
                onClick={() => {
                  handleRestoreHistory(selectedWizard.id);
                }}
              >
                <RestoreOutlined className="inline-block mr-2" color={'var(--text-secondary)'} />
                {t.ai.restore_conversation}
              </div>
              {globalContext.appEnv !== 'PRODUCTION' && (
                <div
                  className="py-2 px-4 cursor-pointer hover:bg-[--hover] flex items-center rounded-[4px]"
                  onClick={() => {
                    // Use the space context to show the AI share modal
                    spaceContext.showUIModal({
                      type: 'ai-share',
                      wizardId: selectedWizard.id,
                    });
                    setIsMenuOpen(false);
                  }}
                >
                  <ShareOutlined className="inline-block mr-2" color={'var(--text-secondary)'} />
                  {t.ai.share_conversation}
                </div>
              )}
              <div
                className={`py-2 px-4 flex items-center ${
                  selectedWizardIndex === 0
                    ? 'cursor-not-allowed !text-[--text-disabled]'
                    : 'cursor-pointer hover:bg-[--hover] !text-[--status-danger] rounded-[4px]'
                }`}
                onClick={async () => {
                  if (selectedWizardIndex === 0) return;
                  Modal.show({
                    type: 'error',
                    title: t.global.copilot.delete_history,
                    content: t.global.copilot.delete_history_confirm,
                    okText: t.delete.delete,
                    cancelText: t.cancel,
                    onOk: () => onDeleteWizard(selectedWizard.id),
                  });
                  setIsMenuOpen(false);
                }}
              >
                <DeleteOutlined
                  className="inline-block mr-2"
                  color={selectedWizardIndex === 0 ? 'var(--text-disabled)' : 'var(--status-danger)'}
                />
                {t.delete.delete}
              </div>
            </div>
          </FloatingFocusManager>
        </FloatingPortal>
      )}
    </>
  );
};
