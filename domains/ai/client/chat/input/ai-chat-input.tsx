'use client';

import type { UseChatHelpers } from '@ai-sdk/react';
import { DragDropContext, Droppable, type DropResult } from '@hello-pangea/dnd';
import { Typography } from '@mui/joy';
import type { SxProps } from '@mui/joy/styles/types';
import { AnimatePresence, motion } from 'framer-motion';
import React, { useRef, memo, useState } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { AIChatInputConfig, AIMessageBO } from '@bika/types/ai/bo';
import type { IAIChatInputStateContext } from '@bika/types/ai/context';
import type { AIChatContextVO } from '@bika/types/ai/vo';
import type { AttachmentVO } from '@bika/types/attachment/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { Button, IconButton } from '@bika/ui/button';
import { useAttachmentUpload } from '@bika/ui/components/image-crop-upload/index';
import { Dropdown, Menu, MenuButton, MenuItem } from '@bika/ui/dropdown';
import { Textarea } from '@bika/ui/form-components';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import ArrowDownOutlined from '@bika/ui/icons/components/arrow_down_outlined';
import AttachmentOutlined from '@bika/ui/icons/components/attachment_outlined';
import PauseOutlined from '@bika/ui/icons/components/pause_outlined';
import SendOutlined from '@bika/ui/icons/doc_hide_components/send_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { useSnackBar } from '@bika/ui/snackbar';
import { VoiceInput, type VoiceInputHandle } from '@bika/ui/voice-input';
import { AIChatInputOptions } from './ai-chat-input-options';
import { AttachmentPreview, type UploadingAttachment } from '../attachment/attachment-preview';
import { AIChatContexts } from '../chat-context/ai-chat-contexts';
import { useChatScrollToBottom } from '../use-chat-scroll-to-bottom';
import { file2Base64 } from '../utils/utils';

export interface BaseAIChatInputProps {
  // user state, 会话，动态 state，用户可改变
  inputState: IAIChatInputStateContext;

  // 配置，不可动的
  config?: AIChatInputConfig;

  // handler: IAIChatSessionHandler;

  // 有什么 context
  // context?: AIChatContextVO[];

  // 自定义选项
  // options?: AIChatOption[];

  sx?: SxProps;

  skillsetIcons?: React.ReactNode;

  // 文件上传接受的文件类型
  uploadAccept?: string;
}

type AIChatInputProps = {
  // input: string;
  // setInput: (input: string) => void;

  disabled: boolean;
  placeholder?: string;
  handleSubmit: (event: { preventDefault?: () => void }) => void;
  isAtBottom?: boolean;
  status?: UseChatHelpers<AIMessageBO>['status'];
  stop?: () => void;
} & BaseAIChatInputProps;

export interface AIChatInputRefHandle {
  textarea: HTMLTextAreaElement;
  state: IAIChatInputStateContext;
  clearInput: () => void;
  // attachments: IInputStore['attachments'];
  // clearContexts: IInputStore['clearAttachments'];
}

function AIChatInputInternal(props: AIChatInputProps, ref: React.Ref<AIChatInputRefHandle>) {
  const { disabled, handleSubmit, sx, isAtBottom = true, status, stop, skillsetIcons } = props;
  const { inputState } = props;
  const option = inputState.option || props.config?.options?.[0];

  const [localInput, _setLocalInput] = useState(inputState.input ?? '');


  const setLocalInput = (v: string) => {
    _setLocalInput(v);
    inputState.setInput(v);
  };

  const uploadAccept = props.config?.uploadAccept || 'image/png, image/jpeg, image/gif, image/webp, application/pdf';

  const isNeedStop = status === 'streaming' || status === 'submitted';
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);
  const [focus, setFocus] = useState(false);
  const voiceInputRef = useRef<VoiceInputHandle>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);
  const { scrollToBottom } = useChatScrollToBottom();
  const locale = useLocale();
  const { t, lang } = locale;
  const { upload: doUploadFile } = useAttachmentUpload();
  const spaceContext = useSpaceContextForce();

  // 附件上传中间态，上传后形成 Chat Context
  const [uploadingAttachments, setUploadingAttachments] = useState<UploadingAttachment[]>([]);
  const chatContexts = inputState.contexts || [];
  const setChatContexts = inputState.setContexts;

  const { toast } = useSnackBar();
  // 空依赖数组确保只在组件挂载时执行一次,  光标自动对焦
  React.useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
      // 将光标移动到最后一个字符
      const length = textareaRef.current.value.length;
      textareaRef.current.setSelectionRange(length, length);
    }
  }, []);

  const uploadFiles = async (files: FileList | null) => {
    if (!files) return;

    const tempUploadingAttachments: { file: File; uploadingAttachment: UploadingAttachment }[] = await Promise.all(
      Array.from(files).map(async (file) => {
        const base64 = await file2Base64(file);
        const uploadingAttachment: UploadingAttachment = {
          id: [file.name, file.type, file.size].join(''),
          base64,
          name: file.name,
          size: file.size,
          mimeType: file.type,
        };
        return { file, uploadingAttachment };
      }),
    );
    const curUploadingAttachments = tempUploadingAttachments.map((item) => item.uploadingAttachment);

    setUploadingAttachments((prev) => [...prev, ...curUploadingAttachments]);

    for (let i = 0; i < tempUploadingAttachments.length; i++) {
      const file = tempUploadingAttachments[i].file;
      const id = tempUploadingAttachments[i].uploadingAttachment.id;
      try {
        const newAttachVO: AttachmentVO = await doUploadFile({
          file,
          filePrefix: 'ai',
        });

        const newContexts: AIChatContextVO[] = [{ type: 'attachment', attachment: newAttachVO }];

        setUploadingAttachments((pre) => pre.filter((item) => item.id !== id));
        setChatContexts((pre) => [...(pre ?? []), ...newContexts]);
      } catch (error) {
        setUploadingAttachments((pre) => pre.filter((item) => item.id !== id));
        const errorMessage = error instanceof Error ? error.message : 'Upload failed';
        toast(`Upload failed: ${file.name} - ${errorMessage}`, {
          variant: 'error',
        });
      }
    }
  };
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragCounter((prev) => prev + 1);
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragCounter((prev) => {
      const newCounter = prev - 1;
      if (newCounter === 0) {
        setIsDragOver(false);
      }
      return newCounter;
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    setDragCounter(0);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const acceptedTypes = uploadAccept.split(',').map((type) => type.trim());
      const validFiles: File[] = [];
      const invalidFiles: File[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const isValidType = acceptedTypes.some((acceptType) => {
          if (acceptType.startsWith('.')) {
            return file.name.toLowerCase().endsWith(acceptType.toLowerCase());
          }
          return file.type.match(acceptType.replace('*', '.*'));
        });

        if (isValidType) {
          validFiles.push(file);
        } else {
          invalidFiles.push(file);
        }
      }

      if (invalidFiles.length > 0) {
        const invalidFileNames = invalidFiles.map((f) => f.name).join(', ');
        toast(t('tips.invalid_file_type_error', { invalidFileNames, uploadAccept }), {
          variant: 'warning',
        });
      }

      if (validFiles.length > 0) {
        const dataTransfer = new DataTransfer();
        validFiles.forEach((file) => dataTransfer.items.add(file));
        uploadFiles(dataTransfer.files);
      }
    }
  };
  const handleDragEnd = (_result: DropResult) => {};
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
      e.preventDefault();
      const inputEle = document.createElement('input');
      inputEle.type = 'file';
      inputEle.multiple = true;
      inputEle.accept = uploadAccept;
      inputEle.onchange = (event) => {
        const files = (event.target as HTMLInputElement).files;
        uploadFiles(files);
      };
      inputEle.click();
    }
  };

  React.useImperativeHandle(ref, () => ({
    textarea: textareaRef.current!,
    // option: option?.value,
    state: inputState,
    clearInput: () => {
      _setLocalInput('');
    },
    // attachments,
    // clearContexts: clearAttachments,
  }));

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId="ai-chat-input-drop-zone">
        {(provided, _snapshot) => (
          <Box
            ref={provided.innerRef}
            {...provided.droppableProps}
            className="flex flex-col shrink-0 bottom-0"
            sx={{
              width: 'calc(100% - 32px)',
              maxWidth: '768px',
              p: 2,
              mb: 2,
              ml: 1,
              mr: 2,
              borderRadius: '10px',
              position: 'relative',
              border: (() => {
                if (isDragOver) return '1px dashed var(--brand)';
                if (focus) return '1px solid var(--brand)';
                return '1px solid var(--border-default)';
              })(),
              // transition: 'all 0.2s ease-in-out',
              ...(isDragOver && {
                background: 'color-mix(in srgb, var(--brand) 10%, var(--bg-elevated) 30%)',
                // boxShadow: '0 0 0 4px color-mix(in srgb, var(--brand) 20%, transparent)',
              }),
              '&:hover': {
                cursor: 'text',
              },
              ...sx,
            }}
            onClick={() => {
              if (textareaRef.current) {
                textareaRef.current.focus();
              }
            }}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onKeyDown={handleKeyDown}
            role="region"
            aria-label="Chat input with file drop zone"
            aria-describedby={isDragOver ? 'drag-drop-instructions' : undefined}
            tabIndex={-1}
          >
            <AnimatePresence>
              {!isAtBottom && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                  className="absolute left-1/2 top-[-32px] -translate-x-1/2 z-50"
                >
                  <IconButton
                    onClick={(event) => {
                      event.preventDefault();
                      event.stopPropagation();
                      scrollToBottom();
                    }}
                    variant="solid"
                    sx={{
                      borderRadius: '50%',
                      background: 'var(--bg-surface)',
                      border: '1px solid var(--border-default)',
                      boxShadow: 'var(--shadow-default)',
                      '&:hover': {
                        background: 'var(--bg-popup)',
                        boxShadow: 'var(--shadow-high)',
                      },
                    }}
                  >
                    <ArrowDownOutlined color={'var(--text-primary)'} />
                  </IconButton>
                </motion.div>
              )}
            </AnimatePresence>
            <Box
              sx={{
                borderRadius: '10px',
              }}
            >
              {/* Drag overlay */}
              {isDragOver && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'var(--bg-mask)',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 10,
                    pointerEvents: 'none',
                  }}
                >
                  <Typography
                    id="drag-drop-instructions"
                    level="b3"
                    textColor="var(--static)"
                    role="status"
                    aria-live="polite"
                  >
                    {t.tips.drop_files_here}
                  </Typography>
                </Box>
              )}

              <div>
                <div className="flex-1">
                  {(!!chatContexts?.length || uploadingAttachments.length > 0) && (
                    <div className="mb-[8px] flex items-center gap-1">
                      {/* Contexts */}

                      <Box
                        className="flex items-center gap-[8px] flex-wrap"
                        sx={{
                          maxHeight: '188px',
                          overflowY: 'auto',
                        }}
                      >
                        <AIChatContexts
                          value={chatContexts}
                          onClickDelete={(clickChatCtxt, idx) => {
                            console.log('onClickDelete', clickChatCtxt);
                            // 从 inputState.contexts 中删除
                            const deletedContexts = chatContexts.filter((_, index) => index !== idx);
                            inputState.setContexts(deletedContexts);
                          }}
                        />
                        {uploadingAttachments.length > 0 &&
                          uploadingAttachments.map((item) => (
                            <AttachmentPreview
                              key={item.id}
                              data={{
                                type: 'uploading',
                                attachment: item,
                              }}
                              onClickDelete={() => {
                                // TODO: 暂停中断上传
                              }}
                            />
                          ))}
                      </Box>

                      {/* <div className="flex items-center gap-[8px] flex-wrap ">
                        <Stack direction={'row'} flexWrap={'wrap'} width={'100%'} columnGap={'8px'} rowGap={'8px'}> */}
                      {/* Uploading statuss */}
                      {/* </Stack>
                      </div> */}
                    </div>
                  )}
                  <Textarea
                    slotProps={{ textarea: { ref: textareaRef } }}
                    sx={{
                      width: '100%',
                      background: 'transparent',
                      boxShadow: 'none',
                      '--Textarea-focusedHighlight': 'transparent !important',
                      fontSize: '16px',
                      lineHeight: '24px',
                      padding: 0,
                      // height: `${Math.min(input.split('\n').length * 20 || 20, 60)}px`,
                      resize: 'none',
                      borderRadius: '8px',
                      '&.Mui-disabled': {
                        border: 0,
                      },
                    }}
                    minRows={2}
                    maxRows={12}
                    onFocus={() => setFocus(true)}
                    onBlur={() => setFocus(false)}
                    onCompositionStart={() => setIsComposing(true)}
                    onCompositionEnd={() => setIsComposing(false)}
                    // 输入框永不 disable，用户可以随时输入，但只是不能发送出去
                    // disabled={disabled}
                    placeholder={props.placeholder || t.ai.type_message} // t.ai.type_message}
                    onKeyDown={(e) => {
                      voiceInputRef.current?.stopListening();
                      const native = e.nativeEvent as unknown as { isComposing?: boolean; keyCode?: number };
                      const composing = native?.isComposing || isComposing || native?.keyCode === 229;

                      if (e.key === 'Enter' && !e.shiftKey) {
                        // 组字中不触发发送
                        if (composing) return;

                        if (!isNeedStop) {
                          e.preventDefault(); // 避免插入换行
                          handleSubmit(e);
                          setLocalInput('');
                        }
                      }
                    }}
                    value={localInput}
                    onChange={(e) => {
                      setLocalInput(e.target.value);
                    }}
                    onPaste={(e) => {
                      const clipboardData = e.clipboardData;
                      if (clipboardData?.files) {
                        const files = Array.from(clipboardData.files).filter((file) => file.type.startsWith('image/'));
                        if (files.length > 0) {
                          e.preventDefault();
                          const fileList = new DataTransfer();
                          for (const file of files) {
                            fileList.items.add(file);
                          }
                          uploadFiles(fileList.files);
                        }
                      }
                    }}
                  />
                </div>
                <div className="flex flex-row justify-between items-end">
                  <Stack display={'flex'} direction={'row'} gap={1} alignItems={'end'}>
                    {props.config?.options && option && (
                      <AIChatInputOptions
                        options={props.config.options}
                        initOption={option}
                        setOption={inputState.setOption}
                      />
                    )}

                    {props.config?.allowContextMenu && (
                      <Dropdown>
                        <MenuButton
                          sx={{
                            width: '32px',
                            height: '32px',
                            padding: '4px',
                            background: 'var(--bg-controls)',
                            border: '1px solid var(--border-default)',
                            '&:hover': {
                              background: 'var(--bg-controls-hover)',
                            },
                          }}
                        >
                          {' '}
                          <AddOutlined color="var(--text-secondary)" />
                        </MenuButton>
                        <Menu>
                          <MenuItem
                            onClick={() => {
                              const inputEle = document.createElement('input');
                              inputEle.type = 'file';
                              inputEle.multiple = true;
                              inputEle.accept = uploadAccept;
                              inputEle.onchange = (e) => {
                                const files = (e.target as HTMLInputElement).files;
                                uploadFiles(files);
                              };
                              inputEle.click();
                            }}
                          >
                            <AttachmentOutlined />
                            <div>{t.global.copilot.upload_file}</div>
                          </MenuItem>
                          <MenuItem
                            onClick={() => {
                              spaceContext?.showUIModal({
                                type: 'node-select',
                                value: {
                                  kind: 'resource',
                                },
                              });
                            }}
                          >
                            <AddOutlined />
                            <div>{t.global.copilot.node_resource}</div>
                          </MenuItem>
                        </Menu>
                      </Dropdown>
                    )}
                    {skillsetIcons}
                  </Stack>

                  <Box
                    className="flex items-center space-x-[8px]"
                    sx={{
                      ml: 'auto',
                    }}
                  >
                    <VoiceInput
                      ref={voiceInputRef}
                      locale={lang}
                      onChange={(text) => {
                        inputState.setInput(text);
                      }}
                    />

                    <Button
                      // loading={status === 'streaming'}
                      disabled={
                        !isNeedStop &&
                        (disabled ||
                          uploadingAttachments.length > 0 ||
                          (!inputState?.input?.trim() && chatContexts.length === 0))
                      }
                      onClick={(e) => {
                        voiceInputRef.current?.stopListening();
                        if (isNeedStop) {
                          stop?.();
                        } else {
                          handleSubmit(e);
                          // it's necessary to clear local value
                          _setLocalInput('');
                        }
                      }}
                      sx={{
                        marginLeft: '8px',
                        '&.Mui-disabled': {
                          backgroundColor: 'var(--brand) !important',
                          opacity: 0.5,
                        },
                      }}
                      color={isNeedStop ? 'danger' : 'primary'}
                      startDecorator={isNeedStop ? <PauseOutlined currentColor /> : <SendOutlined currentColor />}
                    >
                      {isNeedStop ? 'stop' : t.action.send}
                    </Button>
                  </Box>
                </div>
              </div>
            </Box>
            {provided.placeholder}
          </Box>
        )}
      </Droppable>
    </DragDropContext>
  );
}

function compareInputState(prev: IAIChatInputStateContext, next: IAIChatInputStateContext) {
  return prev.input === next.input && prev.contexts === next.contexts && prev.skillsets === next.skillsets;
}

export const AIChatInput = memo(
  React.forwardRef(AIChatInputInternal),
  (prevProps, nextProps) =>
    compareInputState(prevProps.inputState, nextProps.inputState) &&
    prevProps.disabled === nextProps.disabled &&
    prevProps.isAtBottom === nextProps.isAtBottom &&
    prevProps.skillsetIcons === nextProps.skillsetIcons &&
    // prevProps.skillsets?.define === nextProps.skillsets?.define &&
    prevProps.status === nextProps.status,
);
