import assert from 'assert';
import { UIMessage, isTool<PERSON>P<PERSON>, getToolName } from 'ai';
import { sleep } from 'sharelib/sleep';
import { test, describe, expect } from 'vitest';
import { getAINodeSystemPrompt } from '@bika/contents/config/server/ai-prompts/ai-node.prompt';
import { MockContext } from '@bika/domains/__tests__/mock';
import { UserSO } from '@bika/domains/user/server/user-so';
import type {
  AIMessageBO,
  AISDKMessage,
  // toAISDKMessage, toAIMessageBO
} from '@bika/types/ai/bo';
import { AINodeIntentResolver } from '../../ai-intents/ai-node-intent-resolver';
import { IntegrationSO } from '../../integration/server/integration-so';
import { FolderSO } from '../../node/server/folder-so';
import { NodeSO } from '../../node/server/node-so';
import { DataStreamUtils } from '../server/ai-chat/data-stream-utils';
import { AIMockSO } from '../server/ai-mock/ai-mock-so';
import { AISO } from '../server/ai-so';

// 互相转换测试

export function testAIMessage(aiMessage: AIMessageBO): UIMessage {
  return aiMessage;
}
export function testAIMessage2(aiMessage: AISDKMessage): AIMessageBO {
  return aiMessage;
}

const mockWizard = async (user: UserSO, folder: FolderSO) => {
  const aiNodeSO = await folder.createChildSimple(user, {
    resourceType: 'AI',
    name: 'ai-node',
  });
  const wizardSO = await AISO.newWizardByUser(user.id, {
    type: 'AI_NODE',
    nodeId: aiNodeSO.id,
  });
  return wizardSO;
};

describe('AI Node Test--Skillset config', () => {
  test('Test AI basic mock', async () => {
    await AIMockSO.sandbox(async () => {
      const { user } = await MockContext.initUserContext();
      const result = await AISO.streamText(
        {
          user,
          prompt: 'Hello, world!',
        },
        {
          model: 'mock',
        },
      );
      console.log('Start streaming...');
      for await (const chunk of result.fullStream) {
        //
        console.log('Streaming...', chunk);
      }

      console.log('End mock streaming...');
      const aimsg = await result.text;
      console.log('get mock text...', aimsg);

      expect(aimsg).toBe('Hello, I am Mock LanguageModelV2!');

      const message: AIMessageBO | undefined = await DataStreamUtils.parseUIMessage(result.toUIMessageStream());

      expect(message).toBeDefined();
      expect(message?.parts.length).toBe(2); // 第一个是 step-start
      assert(message?.parts[1].type === 'text');
      expect(message?.parts[1].text).toBe('Hello, I am Mock LanguageModelV2!');
    });
  });

  test('Test AI Node-no tools available', async () => {
    const userPrompt = "the japan's capital is ?";
    const assistantResponse = "Japan's capital is Tokyo.";
    AIMockSO.addFilter({
      check: async (opts) => {
        if (
          opts.prompt.find(
            (p) => p.role === 'user' && p.content.find((c) => c.type === 'text' && c.text === userPrompt),
          )
        ) {
          return true;
        }
        return false;
      },
      doStream: assistantResponse,
    });

    await AIMockSO.sandbox(async () => {
      const { user, rootFolder } = await MockContext.initUserContext();

      const result0 = await AISO.streamText(
        {
          user,
          prompt: userPrompt,
        },
        {
          model: 'mock',
        },
      );
      const message0: AIMessageBO | undefined = await DataStreamUtils.parseUIMessage(result0.toUIMessageStream());
      expect(message0).toBeDefined();
      expect(message0?.parts?.find((part) => part.type === 'text')?.text).toBe(assistantResponse);

      // 上面是直接 stream 版，下面是 chat 版，理论完全一样
      const wizard = await mockWizard(user, rootFolder);
      const ctx = MockContext.createMockRequestContext(user);

      const result = await wizard.resolve(
        ctx,
        {
          type: 'MESSAGE',
          message: {
            id: 'test-message-id',
            role: 'user',
            parts: [
              {
                type: 'text',
                text: userPrompt,
              },
            ],
          },
        },
        // null!,
        'en',
        // undefined,
      );

      expect(result.resolution.result).toBeDefined();
      const message = await DataStreamUtils.parseUIMessage(result.resolution.result);
      expect(message).toBeDefined();
      console.log('message parts', message?.parts, message);
      expect(message?.parts!.length).toBe(2);
      expect(message?.parts![1].type).toBe('text');
      assert(message?.parts![1].type === 'text');
      expect(message?.parts![1].text).toBe(assistantResponse);
    });
  });

  test('Test AI Node-tool-search_nodes', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    await space.installTemplateById(user, 'okr-tracker-quarterly-report-automation');
    // wait es index
    await sleep(1000);
    const ctx = MockContext.createMockRequestContext(user);
    const result = await wizard.resolve(
      ctx,
      {
        type: 'MESSAGE',
        message: {
          id: 'test-message-id',
          role: 'user',
          parts: [
            {
              type: 'text',
              text: 'okr',
            },
          ],
        },
      },
      'en',
      // dataStream,
    );
    const message = await DataStreamUtils.parseUIMessage(result.resolution.result);
    console.log('message === ', message);
    expect(message?.parts!.length).toBe(3);
    expect(message?.parts![0].type).toBe('text');
    expect(isToolUIPart(message!.parts![1]) && getToolName(message!.parts[1])).toBe('search_nodes');
    expect(message?.parts![2].type).toBe('text');
  });

  test('Test AI Node-tool-[search_nodes, get_database_records]', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    await space.installTemplateById(user, 'okr-tracker-quarterly-report-automation');
    // wait es index
    await sleep(1000);

    const ctx = MockContext.createMockRequestContext(user);
    const result = await wizard.resolve(
      ctx,
      {
        type: 'MESSAGE',
        message: {
          id: 'test-message-id',
          role: 'user',
          parts: [
            {
              type: 'text',
              text: 'My OKR details for this quarter',
            },
          ],
        },
      },
      'en',
      // dataStream,
    );

    const message = await DataStreamUtils.parseUIMessage(result.resolution.result);

    console.log('message parts', message?.parts);
  });
});

describe('AI node intent resolver--getModel', () => {
  test('get model -- not config', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);

    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const model = await resolver.getModelSelect();
    expect(model.kind).toBe('auto');
    // expect(model.model).toBe('gpt-4.1');
  });

  test('get model -- config with only model--include in ai model config', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        model: {
          kind: 'preset',
          model: 'openai/gpt-4o',
        },
      },
    });
    const model = await resolver.getSDKModel();
    // expect(model.modelId).includes('gpt-4o');
    expect(model.modelId).toSatisfy((v: string) => v.includes('gpt-4o') || v.includes('mock-model-id'));
  });

  test('get model -- config with only model--not include in ai model config', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        model: {
          kind: 'preset',
          model: 'openai/gpt-4.1',
        },
        // model: 'gpt-4o-custom',
      },
    });
    const model = await resolver.getSDKModel();
    // expect(model.modelId).includes('gpt-4.1');
    expect(model.modelId).toSatisfy((v: string) => v.includes('gpt-4.1') || v.includes('mock-model-id'));
  });

  test('get model -- config with only ai model -- OPENAI', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        // aiModel: 'OPENAI',
        model: {
          kind: 'auto',
        },
      },
    });
    const model = await resolver.getSDKModel();
    expect(model.modelId).toSatisfy((v: string) => v.includes('qwen') || v.includes('mock-model-id'));

    // expect(model.ba).toBe(undefined);
    // expect(model.apiKey).toBe(undefined);
    // expect(model.organizationId).toBe(undefined);
  });

  test('get model -- config with only ai model -- DEEPSEEK', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        model: {
          kind: 'preset',
          model: 'deepseek/deepseek-r1',
        },
      },
    });
    const model = await resolver.getSDKModel();
    // expect(model.modelId).includes('deepseek-r1');
    expect(model.modelId).toSatisfy((v: string) => v.includes('deepseek-r1') || v.includes('mock-model-id'));
    // expect(model.baseUrl).toBe(undefined);
    // expect(model.apiKey).toBe(undefined);
    // expect(model.organizationId).toBe(undefined);
  });

  test('get model -- config with integration--openai--default', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    // integration without baseUrl
    const integration = await IntegrationSO.createForSpace(user.id, space.id, {
      name: 'test',
      type: 'OPENAI',
      apiKey: 'test',
    });
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        model: {
          kind: 'custom',
          custom: {
            type: 'integration',
            integrationId: integration.id,
            modelId: 'gpt-4o',
          },
        },
      },
    });
    const model = await resolver.getSDKModel();
    expect(model.modelId).toBe('gpt-4o');
    // expect(model.baseUrl).toBe('https://api.openai.com/v1');
    // expect(model.apiKey).toBe('test');
    // expect(model.organizationId).toBe(undefined);
  });

  test('get model -- config with integration--openai--custom', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    // integration without baseUrl
    const integration = await IntegrationSO.createForSpace(user.id, space.id, {
      name: 'test',
      type: 'OPENAI',
      apiKey: 'test',
      baseUrl: 'https://test/openai/api',
      organizationId: 'test-org',
    });
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        model: {
          kind: 'custom',
          custom: {
            type: 'integration',
            integrationId: integration.id,
            modelId: 'gpt-4.1-custom',
          },
        },
        // model: 'gpt-4o-custom',
      },
    });
    const model = await resolver.getSDKModel();
    expect(model.modelId).includes('gpt-4.1-custom');
    // expect(model.baseUrl).toBe('https://test/openai/api');
    // expect(model.apiKey).toBe('test');
    // expect(model.organizationId).toBe('test-org');
  });

  test('get model -- config with--aiModel--custom', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        model: {
          kind: 'custom',
          custom: {
            type: 'manual',
            provider: {
              type: 'OPENAI',
              apiKey: 'test',
              baseUrl: 'https://api.openai.com/v1',
            },
            modelId: 'gpt-4o',
          },
        },
      },
    });
    const model = await resolver.getSDKModel();
    expect(model.modelId).toBe('gpt-4o');
    // expect(model.baseUrl).toBe('https://api.openai.com/v1');
    // expect(model.apiKey).toBe('test');
    // expect(model.organizationId).toBe(undefined);
  });

  test('get model -- config with--aiModel--custom-model', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        aiModel: {
          type: 'OPENAI',
          apiKey: 'test',
          baseUrl: 'https://test/openai/api',
          organizationId: 'test-org',
        },
        model: {
          kind: 'preset',
          model: 'deepseek/deepseek-v3',
        },
      },
    });
    const model = await resolver.getSDKModel();
    // expect(model.modelId).includes('deepseek-v3');
    expect(model.modelId).toSatisfy((v: string) => v.includes('deepseek-v3') || v.includes('mock-model-id'));

    // expect(model.baseUrl).toBe('https://test/openai/api');
    // expect(model.apiKey).toBe('test');
    // expect(model.organizationId).toBe('test-org');
  });
});

describe('AI node intent resolver--parsePrompt', () => {
  test('parse prompt -- not config', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    const prompt = await resolver.parsePrompt(user, []);
    const systemPrompt = getAINodeSystemPrompt({
      spaceId: aiNode.spaceId,
      parentId: aiNode.parentId!,
      userId: user.id,
    });
    expect(prompt.system).toBe(systemPrompt);
  });

  test('parse prompt -- config with node-resource', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        sources: [
          {
            type: 'NODE',
            nodeId: '123',
          },
        ],
      },
    });
    const prompt = await resolver.parsePrompt(user, []);
    const systemPrompt = getAINodeSystemPrompt({
      spaceId: aiNode.spaceId,
      parentId: aiNode.parentId!,
      userId: user.id,
      nodeIds: ['123'],
    });
    expect(prompt.system).toBe(systemPrompt);
  });

  test('parse prompt -- config with automation and node-resource', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const wizard = await mockWizard(user, rootFolder);
    const resolver = wizard.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        sources: [
          {
            type: 'NODE',
            nodeId: '123',
          },
        ],
        tools: [
          {
            type: 'AUTOMATION',
            automationId: '456',
          },
        ],
      },
    });
    const prompt = await resolver.parsePrompt(user, []);
    const systemPrompt = getAINodeSystemPrompt({
      spaceId: aiNode.spaceId,
      parentId: aiNode.parentId!,
      userId: user.id,
      nodeIds: ['123'],
      automationIds: ['456'],
    });
    // 对比后 100 个字符一致
    expect(prompt.system!.slice(-100)).toBe(systemPrompt.slice(-100));
  });
});

describe('AI node intent resolver--credit cost', () => {
  test('do not cost credit when model is custom', async () => {
    const { user, rootFolder, space } = await MockContext.initUserContext();
    const chat = await mockWizard(user, rootFolder);
    const resolver = chat.intent.resolver as AINodeIntentResolver;
    const aiNode = await NodeSO.init(resolver.intentParams.nodeId!);
    await aiNode.update(user, {
      resourceType: 'AI',
      bo: {
        model: {
          kind: 'custom',
          custom: {
            type: 'manual',
            modelId: 'gpt-4.1',
          },
        },
      },
    });
    const originalCoinAccount = await space.billing.getCoinsAccount();
    const originalVirtualCredit = await originalCoinAccount.virtualCredit();
    // mock filter
    AIMockSO.addFilter({
      check: async (_opts) => true,
      doStream: 'this is mock data',
    });
    await AIMockSO.sandbox(async () => {
      const result = await chat.resolve(
        MockContext.createMockRequestContext(user),
        {
          type: 'MESSAGE',
          message: {
            id: 'test-message-id',
            role: 'user',
            parts: [
              {
                type: 'text',
                text: 'hello',
              },
            ],
          },
          // 'hello',
        },
        'en',
        // dataStream,
      );
      const message = await DataStreamUtils.parseUIMessage(result.resolution.result);
      expect(message).not.toBeNull();
      const newCoinAccount = await space.billing.getCoinsAccount();
      const newVirtualCredit = await newCoinAccount.virtualCredit();
      // check credit cost
      expect(newVirtualCredit).toBe(originalVirtualCredit);
      // check transaction
      const transactions = await newCoinAccount.getTransactions();
      expect(transactions.length).toBe(0);
    });
  });
});
