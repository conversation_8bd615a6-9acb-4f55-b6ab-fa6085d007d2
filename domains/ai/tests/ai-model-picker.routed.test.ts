import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  PresetLanguageModelServerConfig,
  type IAILanguageModelConfig,
  getDefaultAIModel,
} from '@bika/contents/config/server/ai/ai-model-config';
import type { PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import { AIMockSO } from '../server/ai-mock/ai-mock-so';
import { AIModelPicker } from '../server/ai-model-picker';

// Note: These tests stub global/mock behaviors and do not perform network calls.

describe('AIModelPicker.withRoutedModel', () => {
  const presetKey = getDefaultAIModel();
  let originalCfg: IAILanguageModelConfig;

  beforeEach(() => {
    originalCfg = PresetLanguageModelServerConfig[presetKey];
    vi.spyOn(AIMockSO, 'isMockAI').mockResolvedValue(false);
  });

  afterEach(() => {
    PresetLanguageModelServerConfig[presetKey] = originalCfg;
    vi.restoreAllMocks();
  });

  it('resolves using first healthy candidate', async () => {
    PresetLanguageModelServerConfig[presetKey] = {
      type: 'MOCK',
      modelId: 'mock-primary',
    } as unknown as IAILanguageModelConfig;

    const calls: number[] = [];
    const result = await AIModelPicker.withRoutedModel({ model: presetKey }, async () => {
      calls.push(1);
      return 'ok';
    });

    expect(result).toBe('ok');
    expect(calls.length).toBe(1);
  });

  it('falls back to next candidate when first invocation throws, and prioritizes healthy next time', async () => {
    const primary: IAILanguageModelConfig = { type: 'MOCK', modelId: 'mock-A' } as unknown as IAILanguageModelConfig;
    const alternate: IAILanguageModelConfig = { type: 'MOCK', modelId: 'mock-B' } as unknown as IAILanguageModelConfig;
    PresetLanguageModelServerConfig[presetKey] = {
      ...(primary as unknown as Record<string, unknown>),
      alternates: [alternate],
    } as IAILanguageModelConfig;

    let attempt = 0;
    const value = await AIModelPicker.withRoutedModel({ model: presetKey }, async () => {
      attempt += 1;
      if (attempt === 1) throw new Error('boom');
      return 'recovered';
    });
    expect(value).toBe('recovered');
    expect(attempt).toBe(2);

    const ordered = AIModelPicker.orderCandidates(presetKey, [primary, alternate]);
    expect(ordered[0]).toEqual(alternate);
  });
});

describe('AIModelPicker.selectLanguageModel', () => {
  const presetKey = getDefaultAIModel();
  let originalCfg: IAILanguageModelConfig;

  beforeEach(() => {
    originalCfg = PresetLanguageModelServerConfig[presetKey];
    vi.spyOn(AIMockSO, 'isMockAI').mockResolvedValue(false);
  });

  afterEach(() => {
    PresetLanguageModelServerConfig[presetKey] = originalCfg;
    vi.restoreAllMocks();
  });

  it('returns a model instance for preset via routed selection', async () => {
    PresetLanguageModelServerConfig[presetKey] = {
      type: 'MOCK',
      modelId: 'mock-select',
    } as unknown as IAILanguageModelConfig;
    const mdl = await AIModelPicker.selectLanguageModel({ kind: 'preset', model: presetKey });
    expect(mdl).toBeTruthy();
  });
});

const mk = (baseUrl: string, modelId: string): IAILanguageModelConfig => ({
  type: 'OPENAI',
  baseUrl,
  modelId,
  apiKey: 'k',
});

describe('AIModel routing via Picker', () => {
  it('round-robins candidates and skips unhealthy when possible', () => {
    const preset: PresetLanguageAIModelDef = 'openai/gpt-4o' as PresetLanguageAIModelDef;
    const c1 = mk('https://a', 'm');
    const c2 = mk('https://b', 'm');
    const c3 = mk('https://c', 'm');
    const order1 = AIModelPicker.orderCandidates(preset, [c1, c2, c3]);
    expect(order1[0]).toEqual(c1);
    const order2 = AIModelPicker.orderCandidates(preset, [c1, c2, c3]);
    expect(order2[0]).toEqual(c2);

    // mark c2 failure (rate limit)
    AIModelPicker.markFailure(preset, c2, { status: 429, message: 'rate limit' });
    const order3 = AIModelPicker.orderCandidates(preset, [c1, c2, c3]);
    // should skip c2 and pick c3 or c1 depending on rrIndex; at least first should not be c2
    expect(order3[0]).not.toEqual(c2);
  });
});
