import assert from 'assert';
import { createAmazonBedrock } from '@ai-sdk/amazon-bedrock';
import { createAzure } from '@ai-sdk/azure';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { createOpenAI } from '@ai-sdk/openai';
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import type { LanguageModelV2 } from '@ai-sdk/provider';
import { MockLanguageModelV2 } from 'ai/test';
import {
  getDefaultAIModel,
  PresetLanguageModelServerConfig,
  type IAILanguageModelConfig,
} from '@bika/contents/config/server/ai/ai-model-config';
import { PresetLanguageAIModelDef, IAIModelCustomSelectBO, IAIModelSelectBO } from '@bika/types/ai/bo';
import type { CustomAIProviderIntegration, OpenAIIntegration } from '@bika/types/integration/bo';
import { AIMockSO } from './ai-mock/ai-mock-so';
import { AIModelOptions } from './types';
import { IntegrationSO } from '../../integration/server/integration-so';

type RouterHealth = { downUntil: number; recentErrors: number; lastErrorAt?: number };
type RouterState = { rrIndex: number; health: Map<string, RouterHealth> };

export class AIModelPicker {
  // --- Routing state & helpers (moved from AIModelRouter) ---
  private static routerState: Map<PresetLanguageAIModelDef, RouterState> = new Map();

  private static getRouterState(presetKey: PresetLanguageAIModelDef) {
    let s = this.routerState.get(presetKey);
    if (!s) {
      s = { rrIndex: 0, health: new Map() };
      this.routerState.set(presetKey, s);
    }
    return s;
  }

  private static classifyError(err: unknown): 'RATE_LIMIT' | 'AUTH' | 'SERVER' | 'NETWORK' | 'OTHER' {
    const e = err as { status?: number; response?: { status?: number }; message?: string; name?: string } | undefined;
    const status: number | undefined = e?.status ?? e?.response?.status;
    const msg: string = String(e?.message ?? '');
    const name: string = String(e?.name ?? '');

    if (status === 429 || /rate[- ]?limit/i.test(msg) || /RateLimit/i.test(name)) return 'RATE_LIMIT';
    if (status === 401 || status === 403) return 'AUTH';
    if ((status && status >= 500) || /ECONNRESET|ETIMEDOUT|ENOTFOUND|fetch failed/i.test(msg)) return 'NETWORK';
    if (status && status >= 400) return 'SERVER';
    return 'OTHER';
  }

  private static backoffMs(kind: ReturnType<typeof AIModelPicker.classifyError>): number {
    switch (kind) {
      case 'RATE_LIMIT':
        return 60_000;
      case 'AUTH':
        return 15 * 60_000;
      case 'NETWORK':
        return 30_000;
      case 'SERVER':
        return 45_000;
      default:
        return 15_000;
    }
  }

  private static isHealthy(now: number, st?: { downUntil: number }): boolean {
    if (!st) return true;
    return now >= (st.downUntil || 0);
  }

  /**
   * Create a stable key for a candidate config
   */
  public static candidateKey(cfg: { type: string; baseUrl?: string; modelId: string }): string {
    return `${cfg.type}|${cfg.baseUrl ?? ''}|${cfg.modelId}`;
  }

  /**
   * Derive the preset key used for routing buckets from options
   */
  public static getPresetKey(options: AIModelOptions): PresetLanguageAIModelDef {
    const m = options.model;
    if (!m || (typeof m === 'object' && m.kind === 'auto')) return AIModelPicker.getSystemAIModel();
    if (typeof m === 'string') return m;
    if (m.kind === 'preset') return m.model;
    // custom: still use system model key for routing buckets
    return AIModelPicker.getSystemAIModel();
  }

  /**
   * Return candidates in a stable round-robin order while skipping unhealthy ones when possible.
   * If all are unhealthy, returns all in RR order (still tries, best effort).
   */
  public static orderCandidates(
    presetKey: PresetLanguageAIModelDef,
    candidates: IAILanguageModelConfig[],
  ): IAILanguageModelConfig[] {
    const state = this.getRouterState(presetKey);
    const now = Date.now();

    const start = state.rrIndex % Math.max(1, candidates.length);
    const rrOrdered = candidates.slice(start).concat(candidates.slice(0, start));

    // bump rr for next call
    state.rrIndex = (state.rrIndex + 1) % Math.max(1, candidates.length);

    const healthy: IAILanguageModelConfig[] = [];
    const unhealthy: IAILanguageModelConfig[] = [];

    for (const c of rrOrdered) {
      const key = this.candidateKey(c);
      const h = state.health.get(key);
      (this.isHealthy(now, h) ? healthy : unhealthy).push(c);
    }

    return healthy.length ? healthy : rrOrdered;
  }

  public static markFailure(presetKey: PresetLanguageAIModelDef, candidate: IAILanguageModelConfig, err: unknown) {
    const state = this.getRouterState(presetKey);
    const key = this.candidateKey(candidate);
    const cls = this.classifyError(err);
    const ms = this.backoffMs(cls);
    const prev = state.health.get(key) || { downUntil: 0, recentErrors: 0 };
    state.health.set(key, {
      downUntil: Date.now() + ms,
      recentErrors: prev.recentErrors + 1,
      lastErrorAt: Date.now(),
    });
  }

  public static markSuccess(presetKey: PresetLanguageAIModelDef, candidate: IAILanguageModelConfig) {
    const state = this.getRouterState(presetKey);
    const key = this.candidateKey(candidate);
    const prev = state.health.get(key);
    if (prev) {
      state.health.set(key, { downUntil: 0, recentErrors: 0 });
    }
  }

  public static parseCustomAISDKProvider(modelConfig: CustomAIProviderIntegration) {
    let provider;
    // if (modelConfig.type === 'OPENAI' || modelConfig.type === 'DEEPSEEK') {
    //   // assert(custom.modelId, 'Model ID is required for custom AI model');

    //   const openAiProvider = createOpenAI({
    //     name: modelId,
    //     apiKey: modelConfig.apiKey,
    //     baseURL: modelConfig.baseUrl,
    //     organization: modelConfig.organizationId,
    //     compatibility: 'strict', // for accurate Tokens Usage
    //   });
    //   return openAiProvider(modelId);
    // }

    if (modelConfig.type === 'AZURE_AI') {
      provider = createAzure({
        apiKey: modelConfig?.apiKey,
        baseURL: modelConfig?.baseUrl,
        apiVersion: modelConfig?.apiVersion,
      });
    } else if (modelConfig.type === 'AMAZON_BEDROCK') {
      provider = createAmazonBedrock({
        accessKeyId: modelConfig.apiKey,
        secretAccessKey: modelConfig.secretAccessKey,
        baseURL: modelConfig?.baseUrl,
        // region: modelConfig.region || 'us-west-2',
      });
    } else if (modelConfig?.type === 'GOOGLE_AI') {
      provider = createGoogleGenerativeAI({
        apiKey: modelConfig.apiKey,
        baseURL: modelConfig.baseUrl,
      });
    } else if (modelConfig?.type === 'DEEPSEEK') {
      provider = createOpenAICompatible({
        name: modelConfig.type,
        apiKey: modelConfig.apiKey,
        baseURL: modelConfig.baseUrl!,
        includeUsage: true, // Include usage information in streaming responses
      });
    } else if (modelConfig?.type === 'OPENAI') {
      provider = createOpenAI({
        name: modelConfig.type,
        apiKey: modelConfig.apiKey,
        baseURL: modelConfig.baseUrl,
        organization: modelConfig.organizationId,
      });
    }
    assert(provider, `Unsupported AI model provider type: ${modelConfig.type}`);
    return provider;
  }

  private static parseCustomAISDKModel(modelProviderIntegration: CustomAIProviderIntegration, modelId: string) {
    const provider = this.parseCustomAISDKProvider(modelProviderIntegration);
    // if (modelProviderIntegration.type === 'DEEPSEEK') {
    //   return (provider as OpenAICompatibleProvider).chatModel(modelId);
    // }
    return provider.languageModel(modelId);
    // throw new Error('Unsupported custom AI model provider type');
  }

  private static async parseCustom(custom: IAIModelCustomSelectBO) {
    if (custom.custom.type === 'manual') {
      assert(custom.custom.provider, 'Provider is required for manual custom AI model');
      assert(custom.custom.modelId, 'Model ID is required for manual custom AI model');
      return this.parseCustomAISDKModel(custom.custom.provider, custom.custom.modelId);
    }
    if (!custom.custom.integrationId) {
      throw new Error('Integration ID is required for custom AI model');
    }

    const integration = await IntegrationSO.initMaybeNull(custom.custom.integrationId);
    if (!integration) {
      throw new Error('Integration is required for custom AI model');
    }
    assert(
      integration.type === 'OPENAI' || integration.type === 'DEEPSEEK',
      'Unsupported integration type for custom AI model',
    );

    const intBO = integration.getBO<OpenAIIntegration>();
    const openAiProvider = createOpenAI({
      name: custom.custom.modelId,
      apiKey: intBO.apiKey,
      baseURL: intBO.baseUrl,
      organization: intBO.organizationId,
    });
    return openAiProvider(custom.custom.modelId || '');
  }

  private static optionsToPresetAIModelDef(options: AIModelOptions): PresetLanguageAIModelDef {
    let theModel: PresetLanguageAIModelDef;
    if (typeof options.model === 'string') {
      theModel = options.model as PresetLanguageAIModelDef;
    } else if (!options.model || options.model?.kind === 'auto') {
      theModel = this.getSystemAIModel();
    } else if (options.model?.kind === 'preset') {
      theModel = options.model.model;
    } else {
      console.error('Error: ', options.model);
      throw new Error(
        'Invalid AI model options provided, expected a string or IAIModelSelectBO with kind "preset" or "auto"',
      );
    }

    return theModel;
  }

  public static getAIProviderByOptions(options: AIModelOptions) {
    const theModel = this.optionsToPresetAIModelDef(options);
    const modelConfig = PresetLanguageModelServerConfig[theModel];
    assert(modelConfig.type !== 'MOCK', 'Cannot use MOCK model to get provider, mock Model only');
    return this.parseCustomAISDKProvider(modelConfig);
  }

  /**
   * Return primary + fallbacks configs for the given options (auto resolves default preset).
   */
  public static getCandidateConfigs(options: AIModelOptions): IAILanguageModelConfig[] {
    const theModel = this.optionsToPresetAIModelDef(options);
    const modelConfig = PresetLanguageModelServerConfig[theModel];
    assert(modelConfig, `AI model config not found for ${theModel}`);
    const fallbacks = modelConfig.alternates ?? [];
    return [modelConfig, ...fallbacks];
  }

  /**
   * Return instantiated SDK models for primary + fallbacks.
   */
  public static getCandidateModels(
    options: AIModelOptions,
  ): Array<{ cfg: IAILanguageModelConfig; model: LanguageModelV2 | MockLanguageModelV2 }> {
    const configs = this.getCandidateConfigs(options);
    // If the primary is MOCK, just return a single mock model (no routing needed)
    if (configs[0]?.type === 'MOCK') {
      return [{ cfg: configs[0], model: AIMockSO.newMockModel() }];
    }
    return configs.map((cfg) => ({ cfg, model: this.parseCustomAISDKModel(cfg, cfg.modelId) }));
  }

  public static async selectLanguageModel(
    modelSelect: IAIModelSelectBO,
  ): Promise<LanguageModelV2 | MockLanguageModelV2> {
    if (modelSelect.kind === 'custom') {
      return this.parseCustom(modelSelect);
    }

    if (modelSelect.kind === 'preset') {
      // Prefer routed selection path to keep a single codepath
      return this.withRoutedModel({ model: modelSelect.model }, async (m) => m);
    }
    // auto
    return this.withRoutedModel({}, async (m) => m);
  }

  /**
   * Execute an invocation using routed model selection with health-aware fallbacks.
   * - custom: invoke directly without routing/health marks
   * - preset/auto: iterate ordered candidates, markSuccess/markFailure accordingly
   */
  public static async withRoutedModel<T>(
    options: AIModelOptions,
    invoke: (model: LanguageModelV2 | MockLanguageModelV2) => Promise<T>,
  ): Promise<T> {
    // Global mock switch
    if (await AIMockSO.isMockAI()) {
      return invoke(AIMockSO.newMockModel());
    }
    // Custom model: no routing
    if (options.model && typeof options.model === 'object' && options.model.kind === 'custom') {
      const mdl = await this.parseCustom(options.model);
      return invoke(mdl);
    }

    const presetKey = this.getPresetKey(options);
    const candidates = this.getCandidateConfigs(options);
    const ordered = this.orderCandidates(presetKey, candidates);

    let lastErr: unknown;
    for (const cfg of ordered) {
      const mdl = cfg.type === 'MOCK' ? AIMockSO.newMockModel() : this.parseCustomAISDKModel(cfg, cfg.modelId);
      try {
        const res = await invoke(mdl);
        this.markSuccess(presetKey, cfg);
        return res;
      } catch (e) {
        this.markFailure(presetKey, cfg, e);
        lastErr = e;
      }
    }
    throw lastErr;
  }

  public static getSystemAIModel(): PresetLanguageAIModelDef {
    return (process.env.SYSTEM_AI_MODEL as PresetLanguageAIModelDef) || getDefaultAIModel();
  }
}
