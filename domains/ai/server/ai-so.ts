import assert from 'assert';
import * as fs from 'fs';
import path from 'path';
import { OpenAIWhisperAudio } from '@langchain/community/document_loaders/fs/openai_whisper_audio';
import {
  stepCountIs,
  StreamTextResult,
  ToolSet,
  streamObject,
  generateText,
  streamText,
  experimental_generateImage as generateImage,
  type LanguageModelUsage,
  convertToModelMessages,
  readUIMessageStream,
  ModelMessage,
} from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { PresetImageModelsServerConfig } from '@bika/contents/config/server/ai/ai-model-config';
import { getAIModelPricesConfig } from '@bika/contents/config/server/pricing/ai/ai-models-price';
import { createToolCallRequestContext } from '@bika/domains/ai-skillset/utils';
import { AttachmentSO } from '@bika/domains/attachment/server/attachment-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { AIUsage, AIIntentParams, AIGenerateImageProps, PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import type { AIMessageBO, IAIModelSelectBO } from '@bika/types/ai/bo';
import type { AIMessageVO } from '@bika/types/ai/vo';
import { CONST_PREFIX_AI_MESSAGE } from '@bika/types/database/vo';
import { AIChatSO } from './ai-chat/ai-chat-so';
import { AIModelPicker } from './ai-model-picker';
import {
  type AIModelOptions,
  type IPromptWithSchema,
  type IAIStreamYield,
  type IStreamChatReturn,
  type IStreamChatPrompt,
  type IStreamTextPrompt,
  type IPrompt,
  enhanceConvertToModelMessages,
} from './types';
import { AISkillsetServerRegistry } from '../../ai-skillset/server-registry';

export { tool } from 'ai';

export class AISO {
  static async newWizardByMember(unitMemberID: string, intent: AIIntentParams) {
    const newWizardSO = await AIChatSO.create('MEMBER', unitMemberID, intent);
    return newWizardSO;
  }

  static async parseAICreditCost(
    aiModelDef: PresetLanguageAIModelDef | IAIModelSelectBO | undefined,
    aiTokenUsage: LanguageModelUsage,
  ): Promise<AIUsage> {
    let costCredit = 0;

    // 强制弄一个 preset，以作为计算基础，即使是 auto 模型
    let calcModelSelect: IAIModelSelectBO;
    if (!aiModelDef || (typeof aiModelDef === 'object' && aiModelDef.kind === 'auto')) {
      calcModelSelect = { kind: 'preset', model: AIModelPicker.getSystemAIModel() };
    } else if (typeof aiModelDef === 'string') {
      calcModelSelect = { kind: 'preset', model: aiModelDef as PresetLanguageAIModelDef };
    } else if (typeof aiModelDef === 'object' && aiModelDef.kind === 'preset') {
      calcModelSelect = aiModelDef;
    } else {
      assert(aiModelDef.kind === 'custom');
      calcModelSelect = aiModelDef as IAIModelSelectBO;
    }
    assert(calcModelSelect && calcModelSelect.kind !== 'auto', 'AI model select kind must not be auto');

    if (calcModelSelect.kind === 'preset') {
      const modelPriceConfig = getAIModelPricesConfig(calcModelSelect.model);
      const { inputTokens, outputTokens } = aiTokenUsage;
      // filter NAN
      if (!_.isNaN(inputTokens) && !_.isNaN(outputTokens)) {
        // 1000 tokens = 1 credit 小数点五入(不四舍)
        costCredit = Math.ceil(
          (modelPriceConfig.inputCredit / 1000) * (inputTokens || 0) +
            (modelPriceConfig.outputCredit / 1000) * (outputTokens || 0),
        );
      } else {
        return {
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          model: calcModelSelect,
          costCredit: 0,
        };
      }
    }
    return {
      ...aiTokenUsage,
      model: calcModelSelect,
      costCredit,
    };
  }

  static async newWizardByUser(userID: string, intent: AIIntentParams) {
    const newWizardSO = await AIChatSO.create('USER', userID, intent);
    return newWizardSO;
  }

  /**
   * 给指定成员发送消息。
   */
  static async sendMessageToMember(member: MemberSO, intent: AIIntentParams, message: string) {
    const chat = await AISO.newWizardByMember(member.id, intent);

    let aiMessage: AIMessageVO | undefined;

    const user = await member.getUser();
    const ctx = createToolCallRequestContext(user, '');
    const result = await chat.resolve(
      ctx,
      {
        type: 'MESSAGE',
        message: {
          id: generateNanoID('ai-msg'),
          role: 'user',
          parts: [{ type: 'text', text: message }],
        },
      },
      'en',
      // dataStream,
    );

    for await (const uiMessage of readUIMessageStream<AIMessageBO>({
      stream: result.resolution.result,
    })) {
      console.log('Current message state:', uiMessage);
      aiMessage = uiMessage;
    }
    return aiMessage;
  }

  /**
   * 将附件的音频转换成文字
   *
   * @param attachmentSO
   * @returns
   */
  public static async audio2TextByAttachment(attachmentSO: AttachmentSO) {
    const blob = await attachmentSO.getObjectBlob();
    const buffer = await blob.arrayBuffer();

    // 创建临存文件到/tmp，用完就删
    const localTmpFilePath = `/tmp/${attachmentSO.path}`;

    // Ensure the directory path
    const directoryPath = path.dirname(localTmpFilePath);
    if (!fs.existsSync(directoryPath)) {
      // If it doesn't exist, create the directory
      fs.mkdirSync(directoryPath, { recursive: true });
    }

    fs.writeFileSync(localTmpFilePath, new Uint8Array(buffer));

    const res = await AISO.audio2Text(localTmpFilePath);

    fs.unlinkSync(localTmpFilePath);
    return res;
  }

  /**
   * Bika内部系统调用AI，不可选择模型和参数，通过环境变量设置好了
   *
   * @param prompt
   * @param options
   */
  public static async systemInvoke(prompt: string): Promise<string> {
    return this.invoke(prompt, {
      model: AIModelPicker.getSystemAIModel(),
      temperature: 0,
    });
  }

  public static async invoke(prompt: string, options: AIModelOptions) {
    return this.invokeByAISDK(prompt, options);
  }

  /**
   * 基于 AI SDK 的流式调用，支持DeepSeek Reasoning （Danger，不支持 user 和 log记录调用，仅限于测试、Make 命令试用
   *
   * @param prompt
   * @param options
   * @returns
   */
  public static async *dangerStreamYield(
    prompt: Pick<IPrompt, 'prompt'>,
    options: AIModelOptions,
    printConsoleDebug: boolean = true,
  ): AsyncGenerator<IAIStreamYield> {
    const aiSDKStream = await AIModelPicker.withRoutedModel(options, async (model) =>
      streamText({
        prompt: prompt.prompt,
        model,
      }),
    );
    // const aiSDKStream = await this.streamText(prompt, options);

    let isReasoning = false;
    let reasoningContent = '';
    let content = '';

    if (printConsoleDebug) process.stdout.write(`${new Date()} - ${options.model} - Start AI streaming......\n`);

    const { fullStream } = aiSDKStream;

    for await (const sPart of fullStream) {
      let chunkStr;
      if (sPart.type === 'reasoning-delta') {
        chunkStr = sPart.text;
        reasoningContent = reasoningContent.concat(chunkStr);
        isReasoning = true;
      } else if (sPart.type === 'text-delta') {
        chunkStr = sPart.text;
        content = content.concat(chunkStr);
        isReasoning = false;
        // else {
        // }
        // } else if (sPart.type === 'step-start' || sPart.type === 'step-finish' || sPart.type === 'finish') {
        // ignore
      } else {
        console.warn('ignore: ', sPart);
        // throw new Error(`Unknown stream part type: ${sPart.type}`);
      }

      if (printConsoleDebug) process.stdout.write(chunkStr || '');

      yield {
        isReasoning,
        chunkContent: chunkStr,
        reasoningContent,
        content,
      };
    }
  }

  /**
   *
   * @param prompt
   * @param debug 是否打印AI过程
   * @returns
   */
  public static async systemInvokeJSON(prompt: string): Promise<unknown> {
    const res = await this.invoke(prompt, {
      model: AIModelPicker.getSystemAIModel(),
      json: true,
    });

    const jsonStr = res as string;
    try {
      return JSON.parse(jsonStr);
    } catch (e) {
      console.log('AI invokeJSON parseJson error', e, ' return Str', jsonStr);
      return {};
    }
  }

  public static async invokeByAISDK(userPrompt: string, options: AIModelOptions) {
    // Use centralized routed invocation with health + fallbacks
    return AIModelPicker.withRoutedModel(options, async (model) => {
      const { text } = await generateText({ model, prompt: userPrompt });
      return text;
    });
  }

  /**
   * AI 生图，通常不要直接调用，用 Attachment.generateImages，有 log 和 usage 记录
   *
   * @param props
   * @returns
   */
  public static async generateImages(
    props: AIGenerateImageProps,
    // prompt: string,
    // size: `${number}x${number}` | undefined = undefined,
    // n: number | undefined = undefined,
  ) {
    const { prompt, size, n } = props;
    const imageModelConfig = PresetImageModelsServerConfig[props.imageModel];
    const provider = AIModelPicker.parseCustomAISDKProvider(imageModelConfig);

    // Check if the provider supports image generation
    if (!('image' in provider) || typeof provider.image !== 'function') {
      throw new Error(
        `Provider type '${imageModelConfig.type}' does not support image generation. Please use a provider that supports image generation (e.g., OpenAI, Azure AI).`,
      );
    }

    const result = await generateImage({
      model: provider.image(imageModelConfig.modelId), // imageModelConfig.modelId // openai.image('dall-e-3'),
      prompt,
      size: size as `${number}x${number}` | undefined,
      n,
    });
    const { images } = result;
    console.log('Generated images:', images, props);
    return images;
  }

  public static async imageToText(imageUrl: string): Promise<string> {
    const provider = AIModelPicker.getAIProviderByOptions({ model: 'openai/gpt-4o' });
    // url to base64
    const response = await fetch(imageUrl);
    const buffer = await response.arrayBuffer();
    const base64 = Buffer.from(buffer).toString('base64');
    const mimeType = response.headers.get('content-type') || 'image/png';
    const imageData = `data:${mimeType};base64,${base64}`;
    const { text } = await generateText({
      model: provider('gpt-4o'),
      messages: convertToModelMessages([
        {
          role: 'user',

          parts: [
            {
              type: 'text',
              text: `请提取并返回图片中的所有文本内容，要求：
1. 保持原有的格式和布局
2. 保留所有换行和空格
3. 按照从上到下、从左到右的顺序排列
4. 不要添加任何解释或描述
5. 如果有表格，请使用适当的格式保持表格结构
6. 只返回文本内容，不要返回其他描述`,
            },
            {
              type: 'file',
              mediaType: mimeType,
              url: imageData,
            },
          ],
        },
      ]),
      system:
        'You are an OCR expert. Extract all text from images while preserving the original formatting, layout, and structure exactly as it appears in the image.',
      maxOutputTokens: 1000,
      temperature: 0, // 设置为0以获得更一致的结果
    });
    return text;
  }

  public static async fileToTextStream(prompt: { messages: ModelMessage[]; system: string }, options: AIModelOptions) {
    const { messages, system } = prompt;
    return AIModelPicker.withRoutedModel(options, async (model) =>
      streamText({
        model,
        system,
        messages,
        onError: (error) => {
          // eslint-disable-next-line no-console
          console.error('fileToTextStream error:', error);
        },
      }),
    );
  }

  public static async streamObject(
    prompt: IPromptWithSchema,
    options: Omit<AIModelOptions, 'onChunk' | 'onStepFinish'>,
  ) {
    return AIModelPicker.withRoutedModel(options as AIModelOptions, async (model) =>
      streamObject({
        model,
        schema: prompt.schema,
        output: 'object',
        mode: 'json',
        prompt: prompt.prompt,
        messages: prompt.messages ? convertToModelMessages(prompt.messages) : undefined,
        system: `${prompt.system}\n <result_format> return the result in json mode, and do not add any other text</result_format>`,
      }),
    );
  }

  /**
   *  调去 AI LLM， streaming 式返回，同时获取AI Message BO和 Usage
   *
   * @param prompt
   * @param options
   * @param dataStreamWriter
   * @param returnPrompts 返回消息体里，用户下一个可以说话的 prompts 建议
   * @returns
   */
  public static async streamChat(
    // 强制使用 messages，确保 chat history 正确被使用
    prompt: IStreamChatPrompt,
    options: AIModelOptions,
    _msgOptions: {
      // 默认 的AISDK，stream，是会卡在 await steps 的
      // 是否直接消耗整个 stream？  默认都自动从后台开始消费，
      // 若 disableAutoStream = true，则消耗则依赖于客户端，客户端 刷新就会被打断，消息无保存，避免消耗更多 tokens
      // 若 autoStream，则浏览器刷新后，直接取回 stream 继续
      disableAutoStream?: boolean;

      // 返回 prompt tips？控制从 DataStream.data 返回
      returnPrompts?: string[];
    },
  ): Promise<IStreamChatReturn> {
    let skillsetTools = prompt.skillsets
      ? await AISkillsetServerRegistry.parseAISDKToolsets(prompt.skillsets, {
          dataStreamWriter: options.dataStreamWriter,
          user: prompt.user,
          space: prompt.space,
        })
      : undefined;

    if (prompt.tools) {
      // 再度合并, skillsets + tools
      skillsetTools = skillsetTools
        ? await AISkillsetServerRegistry.mergeToolSet([prompt.tools, skillsetTools])
        : prompt.tools;
    }

    // reassign
    // eslint-disable-next-line no-param-reassign
    prompt.tools = skillsetTools;

    const result = await this.streamText(prompt, options);

    return {
      result: result.toUIMessageStream({
        messageMetadata: ({ part }) => {
          if (part.type === 'start') {
            return {
              aiModel: options?.model,
              skillsets: prompt?.skillsets,
            };
          }
          // message will be merged, so no need to return metadata for other parts
          return undefined;
        },
        generateMessageId: () => generateNanoID(CONST_PREFIX_AI_MESSAGE),
        sendSources: true,
      }),
      usage: async () => {
        const sdkUsage = await result.totalUsage;
        const usage = await this.parseAICreditCost(options.model, sdkUsage);
        return usage;
      },
      prompt,
      options,
    };
  }

  /**
   * 不带 usage cost 统计、不带 skillsets parsing，如是聊天，请使用 streamChat
   *
   * @param prompt
   * @param options
   * @returns
   */
  public static async streamText(
    prompt: IStreamTextPrompt,
    options: AIModelOptions,
  ): Promise<StreamTextResult<ToolSet, never>> {
    const messages = prompt.messages ? enhanceConvertToModelMessages(prompt.messages) : undefined;
    return AIModelPicker.withRoutedModel(options, async (model) =>
      streamText({
        model,
        onFinish: async (data) => {
          if (options.onFinish) options.onFinish(data);
        },
        onChunk: (event) => {
          if (options.onChunk) {
            options.onChunk(event);
          }
        },
        onError: (error) => {
          if (options.onError) {
            options.onError(error);
          }
        },
        onStepFinish: (stepResult) => {
          if (options.onStepFinish) {
            options.onStepFinish(stepResult);
          }
        },
        prompt: prompt.prompt,
        system: prompt.system,
        messages,
        tools: prompt.tools,
        stopWhen: stepCountIs(prompt.maxSteps || 20),
      }),
    );
  }

  /**
   * 音频到文字
   *
   * @param audioPath
   * @returns
   */
  public static async audio2Text(audioPath: string | Blob): Promise<string | null> {
    const loader = new OpenAIWhisperAudio(audioPath);
    const docs = await loader.load();
    if (docs.length > 0) {
      return docs[0].pageContent;
    }
    return null;
  }
}
