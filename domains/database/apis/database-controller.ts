import assert from 'assert';
import fs from 'fs';
import os from 'os';
import path from 'path';
import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { ViewSO } from '@bika/domains/database/server/views/view-so';
import { FormSO } from '@bika/domains/form/server';
import { ExcelImporter, ExcelWorkbookWrapper } from '@bika/domains/integration/server/handlers/excel-importer-handler';
import { FolderSO } from '@bika/domains/node/server/folder-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { LockUtil, Logger } from '@bika/domains/shared/server';
import { UserSO } from '@bika/domains/user/server/user-so';
import { TRPCError } from '@bika/server-orm/trpc';
import { DatabaseFieldWithId, RecordData } from '@bika/types/database/bo';
import {
  RecordListDTO,
  FieldGetDTO,
  ViewDeleteDTO,
  RecordDetailDTO,
  RecordCreateDTO,
  RecordDeleteDTO,
  FieldCreateDTO,
  FieldUpdateDTO,
  FieldDeleteDTO,
  RecordUpdateDTO,
  RecordBulkUpdateDTO,
  ViewInfoDTO,
  ViewUpdateDTO,
  DatabaseInfoDTO,
  RecordCommentListDTO,
  RecordAddCommentDTO,
  LinkDatabaseDTO,
  FieldListDTO,
  FieldROListDTO,
  RecordActivityListDTO,
  LinkDatabaseInfoDTO,
  DatabaseViewCreateDTO,
  RecordBulkUpdatesDTO,
  RecordsCreateDTO,
  RecordDetailListDTO,
} from '@bika/types/database/dto';
import { DatabaseFieldRO } from '@bika/types/database/ro';
import {
  DatabaseVO,
  RecordDetailVO,
  ViewVO,
  RecordActivityPaginationVO,
  RecordPaginationVO,
  RecordRenderVO,
  RecordCommentPaginationVO,
  FieldVO,
  RecordDetailsVO,
} from '@bika/types/database/vo';
import { Locale } from '@bika/types/i18n/bo';
import { ExcelImportDTO } from '@bika/types/node/dto';
import { UsageExceedLimitErrorData } from '@bika/types/pricing/vo';
import { SSEEventDatabaseRowUpdatedData } from '@bika/types/user/bo';
import { ApiFetchRequestContext } from '@bika/types/user/vo';
import { TmpAttachmentSO } from '../../attachment/server/tmp-attachment-so';
import { DocSO } from '../../doc/server/doc-so';
import { SseSO } from '../../event/server/sse/sse-so';
import { PRIVATE_ROOT_NODE_PREFIX } from '../../node/server/types';
import { CommentSO } from '../server/record/comment-so';

const { size } = _;

/**
 * 检查用量
 *
 * @param database 数据库
 * @param numOfNewRecords 新增记录数
 */
const checkEntitlement = async (database: DatabaseSO, numOfNewRecords: number) => {
  const space = await database.getSpace();
  const entitlement = await space.getEntitlement();
  await entitlement.checkRecordsPerDatabaseUsage(database.id, numOfNewRecords);
  await entitlement.checkUsageExceed({ feature: 'RECORDS_PER_SPACE', value: numOfNewRecords });
};

export async function info(ctx: ApiFetchRequestContext, req: DatabaseInfoDTO): Promise<DatabaseVO> {
  const { session, locale } = ctx;
  const { databaseId } = req;
  const database = await DatabaseSO.init(databaseId);
  const aclSO = await database.toAclSO();
  if (session?.userId) {
    const user = await UserSO.init(session!.userId);
    await aclSO.authorize(user, 'readNode');
    return database.toVO({ locale: user.locale });
  }
  // 是否可公开访问
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return database.toVO({ locale });
}

export async function getView(ctx: ApiFetchRequestContext, req: ViewInfoDTO): Promise<ViewVO> {
  const { session, locale } = ctx;
  const { viewId, mirrorId } = req;
  const view = await ViewSO.init(viewId);
  if (session?.userId) {
    const user = await UserSO.init(session!.userId);
    if (mirrorId) {
      const mirror = await view.database.getMirror(mirrorId);
      const mirrorAclSO = await mirror.toAclSO();
      await mirrorAclSO.authorize(user, 'readNode');
    } else {
      const aclSO = await view.database.toAclSO();
      await aclSO.authorize(user, 'readNode');
    }
    return view.toVO({ locale: user.locale });
  }
  const aclSO = await view.database.toAclSO();
  // 是否可公开访问
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    if (!mirrorId) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
    const mirror = await NodeSO.init(mirrorId);
    const mirrorAclSO = await mirror.toAclSO();
    const isMirrorPublicAccess = await mirrorAclSO.isPublicAccess();
    if (!isMirrorPublicAccess) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
  }
  return view.toVO({ locale });
}

export async function createView(user: UserSO, req: DatabaseViewCreateDTO): Promise<ViewVO> {
  const { databaseId } = req;
  const database = await DatabaseSO.init(databaseId);
  const aclSO = await database.toAclSO();
  await aclSO.authorize(user, 'updateNode');
  const view = await database.createView(user, req);
  return view.toVO({ locale: user.locale });
}

export async function getLinkDatabase(
  ctx: ApiFetchRequestContext,
  req: LinkDatabaseDTO,
): Promise<{ [fieldId: string]: DatabaseVO }> {
  const { databaseId } = req;
  const database = await DatabaseSO.init(databaseId);
  // 拿到{field: databaseSO}
  const linkedDatabaseMap = await database.getLinkedDatabaseMap();
  // 合并请求
  const databaseEntries: [string, DatabaseVO][] = await Promise.all(
    Object.entries(linkedDatabaseMap).map(async ([fieldId, linkedDatabase]) => {
      const databaseVO = await linkedDatabase.toVO({ locale: ctx.locale });
      return [fieldId, databaseVO];
    }),
  );
  // 输出
  return Object.fromEntries(databaseEntries);
}

// 跨表单来查询表单所在表的关联字段对应的表
// example1: form -> formDatabase -> linkFieldId -> database
// example2: database -> linkFieldId -> database
export async function getLinkDatabaseInfo(ctx: ApiFetchRequestContext, req: LinkDatabaseInfoDTO): Promise<DatabaseVO> {
  const { session, locale } = ctx;
  const { formId, databaseId, linkFieldId } = req;
  const database = await DatabaseSO.init(databaseId);

  if (session?.userId) {
    // 登录了, 验证一下权限
    const user = await UserSO.init(session!.userId);
    if (formId) {
      // 从表单过来的
      const form = await database.getForm(formId);
      const formAclSO = await form.toAclSO();
      await formAclSO.authorize(user, 'readNode');
    } else {
      const databaseACL = await database.toAclSO();
      await databaseACL.authorize(user, 'readNode');
    }

    const linkedDatabase = await database.getLinkDatabaseByFieldId(linkFieldId);
    if (!linkedDatabase) {
      throw new ServerError(errors.database.link_database_not_found);
    }
    return linkedDatabase.toVO({ locale: user.locale });
  }

  // 是否可公开访问
  const databaseACL = await database.toAclSO();
  const isPublicAccess = await databaseACL.isPublicAccess();
  if (!isPublicAccess) {
    if (!formId) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
    const form = await database.getForm(formId);
    const formAclSO = await form.toAclSO();
    const isFormPublicAccess = await formAclSO.isPublicAccess();
    if (!isFormPublicAccess) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
  }
  const linkedDatabase = await database.getLinkDatabaseByFieldId(linkFieldId);
  if (!linkedDatabase) {
    throw new ServerError(errors.database.link_database_not_found);
  }
  return linkedDatabase.toVO({ locale });
}

export async function updateView(user: UserSO, req: ViewUpdateDTO) {
  const { viewId, data } = req;
  const view = await ViewSO.init(viewId);
  const aclSO = await view.database.toAclSO();
  await aclSO.authorize(user, 'updateNode');
  // db operation
  await view.update(user.id, data);
}

export async function deleteView(user: UserSO, req: ViewDeleteDTO) {
  const { viewId } = req;
  const view = await ViewSO.init(viewId);
  const database = view.database;
  const aclSO = await database.toAclSO();
  await aclSO.authorize(user, 'updateNode');
  if (size(database.viewModels) === 1) {
    throw new Error('Cannot delete the last view of a database');
  }
  await view.delete();
}

export async function getFieldBOs(ctx: ApiFetchRequestContext, req: FieldListDTO): Promise<DatabaseFieldWithId[]> {
  const { session } = ctx;
  const { databaseId } = req;
  const database = await DatabaseSO.init(databaseId);
  if (session?.userId) {
    const user = await UserSO.init(session.userId);
    const aclSO = await database.toAclSO();
    await aclSO.authorize(user, 'readNode');
    const fields = database.getFields();
    return fields.map((field) => field.toBO());
  }
  // 是否可公开访问
  const aclSO = await database.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return database.getFields().map((field) => field.toBO());
}

export async function getFieldROs(ctx: ApiFetchRequestContext, req: FieldROListDTO): Promise<DatabaseFieldRO[]> {
  const { session } = ctx;
  const { databaseId } = req;
  const database = await DatabaseSO.init(databaseId);
  if (session?.userId) {
    const user = await UserSO.init(session.userId);
    const aclSO = await database.toAclSO();
    await aclSO.authorize(user, 'readNode');
    const fields = database.getFields();
    return Promise.all(fields.map(async (field) => field.toRO()));
  }
  // 是否可公开访问
  const aclSO = await database.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return Promise.all(database.getFields().map(async (field) => field.toRO()));
}

export async function getField(ctx: ApiFetchRequestContext, req: FieldGetDTO): Promise<DatabaseFieldWithId> {
  const { session } = ctx;
  const { databaseId, fieldId } = req;
  const database = await DatabaseSO.init(databaseId);
  const field = database.getFieldByFieldKey(fieldId);
  const aclSO = await database.toAclSO();
  if (session?.userId) {
    const user = await UserSO.init(session!.userId);
    await aclSO.authorize(user, 'readNode');
    return field.toBO();
  }
  // 是否可公开访问
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return field.toBO();
}

export async function createField(user: UserSO, req: FieldCreateDTO): Promise<FieldVO> {
  const { databaseId, viewId, field } = req;
  if (field.templateId) {
    throw new TRPCError({ code: 'BAD_REQUEST', message: 'Cannot specify the templateId' });
  }
  let database = await DatabaseSO.init(databaseId);
  const aclSO = await database.toAclSO();
  await aclSO.authorize(user, 'updateNode');

  // Do add field
  const addedFieldSO = await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);

    return database.createField(user, field, { viewId });
  });

  return addedFieldSO.toVO({ locale: user.locale });
}

export async function updateField(user: UserSO, req: FieldUpdateDTO): Promise<FieldVO> {
  const { databaseId, field } = req;
  let database = await DatabaseSO.init(databaseId);
  const aclSO = await database.toAclSO();
  await aclSO.authorize(user, 'updateNode');

  // Do update field
  const updatedFieldSO = await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);

    const fieldSO = database.getFieldByFieldKey(field.id);

    return fieldSO.update(user, field);
  });

  return updatedFieldSO.toVO({ locale: user.locale });
}

export async function deleteField(user: UserSO, req: FieldDeleteDTO): Promise<void> {
  const { databaseId, fieldId } = req;
  let database = await DatabaseSO.init(databaseId);
  const aclSO = await database.toAclSO();
  await aclSO.authorize(user, 'updateNode');

  await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);

    const fieldSO = database.getFieldByFieldKey(fieldId);

    await fieldSO.delete(user);
  });
}

export async function listRecords(
  ctx: ApiFetchRequestContext,
  req: RecordListDTO,
  opts?: { useTempView?: boolean },
): Promise<RecordPaginationVO> {
  const { session, locale } = ctx;
  const user = session?.userId ? await UserSO.init(session.userId) : undefined;
  const { useTempView = false } = opts ?? {};

  const {
    databaseId,
    mirrorId,
    formId,
    filter,
    sort,
    group,
    groupSelect,
    keyword,
    startRow,
    endRow,
    withCommentCount,
    viewId,
  } = req;

  const database = await DatabaseSO.init(databaseId);
  let view: ViewSO | null = null;

  if (user) {
    if (mirrorId) {
      const mirror = await database.getMirror(mirrorId);
      const mirrorAclSO = await mirror.toAclSO();
      await mirrorAclSO.authorize(user, 'readNode');
      view = await mirror.getViewSO();
    } else if (formId) {
      const form = await FormSO.init(formId);
      const formAclSO = await form.toAclSO();
      await formAclSO.authorize(user, 'readNode');
      const formDatabase = await form.getDatabase();
      if (!formDatabase) {
        throw new Error('Form database not found');
      }
      const foundDatabase = await formDatabase.getLinkDatabaseByDatabaseId(database.id);
      view = foundDatabase && (await foundDatabase?.firstView());
    } else {
      const aclSO = await database.toAclSO();
      await aclSO.authorize(user, 'readNode');
    }
  } else {
    const aclSO = await database.toAclSO();
    const isPublicAccess = await aclSO.isPublicAccess();
    if (!isPublicAccess) {
      if (mirrorId) {
        const mirror = await database.getMirror(mirrorId);
        const mirrorAclSO = await mirror.toAclSO();
        const isMirrorPublicAccess = await mirrorAclSO.isPublicAccess();
        if (!isMirrorPublicAccess) {
          throw new TRPCError({ code: 'UNAUTHORIZED' });
        }
        view = await mirror.getViewSO();
      } else if (formId) {
        const form = await FormSO.init(formId);
        const formAclSO = await form.toAclSO();
        const isFormPublicAccess = await formAclSO.isPublicAccess();
        if (!isFormPublicAccess) {
          throw new TRPCError({ code: 'UNAUTHORIZED' });
        }
        const formDatabase = await form.getDatabase();
        if (!formDatabase) {
          throw new Error('Form database not found');
        }
        const foundDatabase = await formDatabase.getLinkDatabaseByDatabaseId(database.id);
        view = foundDatabase && (await foundDatabase?.firstView());
      } else {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }
    }
  }

  if (!view) {
    if (viewId) {
      view = await database.getView(viewId);
    } else {
      view = useTempView ? ViewSO.createTempView(database) : await database.firstView();
    }
  }

  view.setAdditionalFilter(filter);
  view.setAdditionalSort(sort);
  view.setAdditionalGroup(group);
  view.setGroupSelect(groupSelect);
  view.setKeyword(keyword);
  view.setStartRow(startRow);
  view.setEndRow(endRow);

  // 查询分组列表
  if (view.isGetGroupList()) {
    const [groups, total] = await Promise.all([view.getGroupRows(user), view.getRecordCount(user)]);
    let nextRow: number | undefined;
    if (startRow + groups.length < total) {
      nextRow = startRow + groups.length;
    }

    const rows = await Promise.all(
      groups.map(async ({ record, groupCount }) => ({
        ...(await record.toRenderVO({ locale: user?.locale ?? locale, timeZone: user?.timeZone })),
        groupCount,
      })),
    );

    return {
      total,
      nextRow,
      rows,
    };
  }

  // 正常查询 records
  const { records, total } = await view.getRecordsWithTotal(user);

  // To VO
  const rows = await Promise.all(
    records.map(async (record) => record.toRenderVO({ locale: user?.locale ?? locale, timeZone: user?.timeZone })),
  );

  // 获取评论数
  if (withCommentCount) {
    const countMap = await CommentSO.getCommentCountByRecordIds(records.map((record) => record.id));
    for (const record of rows) {
      record.commentCount = countMap[record.id] || 0;
    }
  }

  let nextRow: number | undefined;
  if (records.length) {
    nextRow = startRow + records.length;
  }

  return {
    total,
    nextRow,
    rows,
  };
}

export async function getRecord(ctx: ApiFetchRequestContext, req: RecordDetailDTO): Promise<RecordDetailVO> {
  const { session, locale } = ctx;
  const { databaseId, viewId, mirrorId, recordId } = req;
  const database = await DatabaseSO.init(databaseId);
  const record = await database.getRecord(recordId);
  if (session?.userId) {
    const user = await UserSO.init(session.userId);
    if (mirrorId) {
      const mirror = await database.getMirror(mirrorId);
      const mirrorAclSO = await mirror.toAclSO();
      await mirrorAclSO.authorize(user, 'readNode');
    } else {
      const aclSO = await database.toAclSO();
      await aclSO.authorize(user, 'readNode');
    }
    return record.toDetailVO({ locale: user.locale, timeZone: user.timeZone, viewId });
  }
  // 是否可公开访问
  const aclSO = await database.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    if (!mirrorId) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
    const mirror = await database.getMirror(mirrorId);
    const mirrorAclSO = await mirror.toAclSO();
    const isMirrorPublicAccess = await mirrorAclSO.isPublicAccess();
    if (!isMirrorPublicAccess) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
  }
  return record.toDetailVO({ locale, viewId });
}

export async function getRecords(ctx: ApiFetchRequestContext, req: RecordDetailListDTO): Promise<RecordDetailsVO> {
  const { session, locale } = ctx;
  const { databaseId, mirrorId, viewId, recordIds } = req;
  const database = await DatabaseSO.init(databaseId);
  const view: ViewSO = viewId ? await database.getView(viewId) : await database.firstView();
  const fields = view.getFields().map((field) => field.toVO({ locale, viewFields: view.fields }));
  const records = await database.getRecords(recordIds);
  if (session?.userId) {
    const user = await UserSO.init(session.userId);
    if (mirrorId) {
      const mirror = await database.getMirror(mirrorId);
      const mirrorAclSO = await mirror.toAclSO();
      await mirrorAclSO.authorize(user, 'readNode');
    } else {
      const aclSO = await database.toAclSO();
      await aclSO.authorize(user, 'readNode');
    }

    const recordVOs = await Promise.all(
      records.map((record) => record.toRenderVO({ locale: user.locale, timeZone: user.timeZone })),
    );

    return { fields, records: recordVOs };
  }
  // 是否可公开访问
  const aclSO = await database.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    if (!mirrorId) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
    const mirror = await database.getMirror(mirrorId);
    const mirrorAclSO = await mirror.toAclSO();
    const isMirrorPublicAccess = await mirrorAclSO.isPublicAccess();
    if (!isMirrorPublicAccess) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
  }
  const recordVOs = await Promise.all(records.map((record) => record.toRenderVO({ locale })));
  return { fields, records: recordVOs };
}

async function getUserAfterCheck(ctx: ApiFetchRequestContext, req: RecordCreateDTO, database: DatabaseSO) {
  const { session } = ctx;
  const { formId, mirrorId, sharing } = req;
  let aclSO;

  // 表单提交场景
  if (formId) {
    const form = await database.getForm(formId);
    aclSO = await form.toAclSO();
    // 分享表单场景
    if (sharing && aclSO.isAnonymousAccess) {
      // 若是设置了匿名提交，即使当前用户已登陆，也不作为创建人存储，以匿名身份提交
      return undefined;
    }
    // 站内/分享同URL后，若当前用户已登陆，且是空间站内成员时，sharing 标志失效，无法区分入口
    if (aclSO.isAnonymousAccess && session?.userId) {
      const user = await UserSO.init(session.userId);
      const hasAccess = await aclSO.can(user, 'createRow');
      // 若是站内成员且有权限，无法区分提交入口，仍以当前用户身份提交，匿名提交失效
      return hasAccess ? user : undefined;
    }
  } else if (mirrorId) {
    const mirror = await database.getMirror(mirrorId);
    aclSO = await mirror.toAclSO();
  } else {
    aclSO = await database.toAclSO();
  }

  if (!session) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  const user = await UserSO.init(session.userId);
  if (formId) {
    // 站内, 只要是可读以上都可以提交
    await aclSO.authorize(user, 'readNode');
  } else {
    await aclSO.authorize(user, 'createRow');
  }
  return user;
}

export async function createRecordsWithUser(
  user: UserSO | undefined,
  locale: Locale,
  dto: RecordsCreateDTO,
): Promise<RecordRenderVO[]> {
  const { databaseId, cells, formId } = dto;

  let database = await DatabaseSO.init(databaseId);

  const member = user ? await user.findMember(database.spaceId) : null;

  // 用量检查
  await checkEntitlement(database, cells.length);

  // Do create record
  const newRecords = await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);

    return database.createRecords(user ?? null, member, cells, { formId });
  });

  const vos = await Promise.all(
    newRecords.map((record) => record.toRenderVO({ locale: user ? user.locale : locale, timeZone: user?.timeZone })),
  );

  await SseSO.emit(user?.id ?? '', {
    name: 'rows-created',
    databaseId,
    data: vos,
    updateBy: user?.id ?? '',
  });

  return vos;
}

export async function createRecordWithUser(
  user: UserSO | undefined,
  locale: Locale,
  dto: RecordCreateDTO,
): Promise<RecordRenderVO> {
  const { databaseId, cells, formId } = dto;

  let database = await DatabaseSO.init(databaseId);

  const member = user ? await user.findMember(database.spaceId) : null;

  // 用量检查
  await checkEntitlement(database, 1);

  // Do create record
  const newRecord = await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);

    const recordToCreate: RecordData = Object.fromEntries(Object.entries(cells).map(([key, data]) => [key, data]));
    return database.createRecord(user ?? null, member, recordToCreate, { formId });
  });

  const vo = await newRecord.toRenderVO({ locale: user ? user.locale : locale, timeZone: user?.timeZone });

  await SseSO.emit(user?.id ?? '', {
    name: 'rows-created',
    databaseId,
    data: [vo],
    updateBy: user?.id ?? '',
  });

  return vo;
}
/**
 * 新增 Record
 */
export async function createRecordWithTRPCContext(
  ctx: ApiFetchRequestContext,
  dto: RecordCreateDTO,
): Promise<RecordRenderVO> {
  const { locale } = ctx;

  const {
    databaseId,
    // cells, formId
  } = dto;

  const database = await DatabaseSO.init(databaseId);
  const user = await getUserAfterCheck(ctx, dto, database);
  return createRecordWithUser(user, locale, dto);
}

/**
 * 更新 Record
 *
 * @param user user
 * @param spaceId space id
 * @param updateRecordVO 更新的 Record
 */
export async function updateRecord(user: UserSO, req: RecordUpdateDTO): Promise<RecordRenderVO> {
  const { databaseId, mirrorId, id: recordId, cells } = req;
  if (recordId.trim().length === 0) {
    throw new Error('record id is required');
  }
  let database = await DatabaseSO.init(databaseId);
  if (mirrorId) {
    const mirror = await database.getMirror(mirrorId);
    const mirrorAclSO = await mirror.toAclSO();
    await mirrorAclSO.authorize(user, 'readNode');
  } else {
    const aclSO = await database.toAclSO();
    await aclSO.authorize(user, 'updateRow');
  }

  const record = await database.getRecord(recordId);
  const oldRecordVo = await record.toRenderVO({ locale: user.locale, timeZone: user.timeZone });

  // Do update record
  const updatedRecordVo = await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);

    const updatedRecords = await database.updateRecords(user, [
      {
        recordId: record.id,
        cells,
      },
    ]);
    return updatedRecords[0].toRenderVO({ locale: user.locale, timeZone: user.timeZone });
  });

  await SseSO.emit(user.id ?? '', {
    name: 'rows-updated',
    databaseId,
    dataList: [
      {
        newValue: updatedRecordVo,
        oldValue: oldRecordVo,
      },
    ],
    updateBy: user?.id ?? '',
  });
  return updatedRecordVo;
}

export async function updateRecords(user: UserSO, req: RecordBulkUpdatesDTO): Promise<RecordRenderVO[]> {
  const { databaseId, mirrorId, updates } = req;
  if (updates.length === 0) {
    throw new Error('no records to update');
  }
  let database = await DatabaseSO.init(databaseId);
  if (mirrorId) {
    const mirror = await database.getMirror(mirrorId);
    const mirrorAclSO = await mirror.toAclSO();
    await mirrorAclSO.authorize(user, 'readNode');
  } else {
    const aclSO = await database.toAclSO();
    await aclSO.authorize(user, 'updateRow');
  }

  const toUpdateRecordIds = updates.map((update) => update.recordId);
  const records = await database.getRecords(toUpdateRecordIds);
  // 原记录视图
  const previousRecordRenderVOs = await Promise.all(
    records.map((record) => record.toRenderVO({ locale: user.locale, timeZone: user.timeZone })),
  );

  // Do update record
  const updatedRecords = await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);
    // 批量更新
    return database.updateRecords(user, updates);
  });

  // 重新刷新数据表
  database = await DatabaseSO.init(databaseId);
  // 更新后的记录视图
  const updatedRecordRenderVOs = await Promise.all(
    updatedRecords.map((record) => record.toRenderVO({ locale: user.locale, timeZone: user.timeZone })),
  );

  // 构建更新后的数据列表
  const rowsUpdatedDataList: SSEEventDatabaseRowUpdatedData[] = [];
  for (const toUpdatedRecordId of toUpdateRecordIds) {
    const previousRecordRenderVO = previousRecordRenderVOs.find((item) => item.id === toUpdatedRecordId);
    const updatedRecordRenderVO = updatedRecordRenderVOs.find((item) => item.id === toUpdatedRecordId);
    if (previousRecordRenderVO && updatedRecordRenderVO) {
      rowsUpdatedDataList.push({
        oldValue: previousRecordRenderVO,
        newValue: updatedRecordRenderVO,
      });
    }
  }

  await SseSO.emit(user.id ?? '', {
    name: 'rows-updated',
    databaseId,
    dataList: rowsUpdatedDataList,
    updateBy: user?.id ?? '',
  });
  // 返回更新后的记录
  return updatedRecordRenderVOs;
}

/**
 * 批量更新 Record
 */
export async function bulkUpdateRecords(user: UserSO, req: RecordBulkUpdateDTO) {
  const {
    databaseId,
    mirrorId,
    match: { recordIds, filters },
    cells,
  } = req;
  if (!recordIds?.length && !filters) {
    throw new Error('recordIds or filters is required');
  }

  let database = await DatabaseSO.init(databaseId);
  if (mirrorId) {
    const mirror = await database.getMirror(mirrorId);
    const mirrorAclSO = await mirror.toAclSO();
    await mirrorAclSO.authorize(user, 'readNode');
  } else {
    const aclSO = await database.toAclSO();
    await aclSO.authorize(user, 'updateRow');
  }

  // Do bulk update records
  const updatedRecords = await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);

    // 批量更新 by recordIds
    if (recordIds?.length) {
      return database.bulkUpdateByRecordIds(user, recordIds, cells);
    }

    // 批量更新 by filters
    if (filters) {
      return database.bulkUpdateByFilter(user, filters, cells);
    }

    throw new Error('Invalid request');
  });

  return Promise.all(
    updatedRecords.map((record) => record.toRenderVO({ locale: user.locale, timeZone: user.timeZone })),
  );
}

/**
 * 删除 Record
 *
 * @param recordIds
 */
export async function deleteRecords(user: UserSO, req: RecordDeleteDTO) {
  const { databaseId, mirrorId, recordIds } = req;
  if (recordIds.length === 0) {
    return;
  }
  let database = await DatabaseSO.init(databaseId);
  if (mirrorId) {
    const mirror = await database.getMirror(mirrorId);
    const mirrorAclSO = await mirror.toAclSO();
    await mirrorAclSO.authorize(user, 'readNode');
  } else {
    const aclSO = await database.toAclSO();
    await aclSO.authorize(user, 'deleteRow');
  }

  // Do delete records
  await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);

    await database.deleteRecords(user, recordIds);
  });

  // TODO 需要优化， 推送空间站表格事件
  await SseSO.emit(user?.id ?? '', {
    name: 'rows-deleted',
    databaseId,
    dataList: recordIds.map((id) => ({ recordId: id })),
    updateBy: user?.id ?? '',
  });
}

export async function getRecordComments(
  ctx: ApiFetchRequestContext,
  req: RecordCommentListDTO,
): Promise<RecordCommentPaginationVO> {
  const { session } = ctx;
  const { databaseId, mirrorId, recordId, pageNo, pageSize } = req;
  const database = await DatabaseSO.init(databaseId);
  const record = await database.getRecord(recordId);
  if (session?.userId) {
    const user = await UserSO.init(session.userId);
    if (mirrorId) {
      const mirror = await database.getMirror(mirrorId);
      const mirrorAclSO = await mirror.toAclSO();
      await mirrorAclSO.authorize(user, 'readNode');
    } else {
      const aclSO = await database.toAclSO();
      await aclSO.authorize(user, 'readNode');
    }
    const { pagination, list: comments } = await record.getComments({ pageNo, pageSize });
    const activities = await Promise.all(comments.map((comment) => comment.toActivityVO()));
    return { pagination, data: activities };
  }
  // 是否可公开访问
  const aclSO = await database.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    if (!mirrorId) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
    const mirror = await database.getMirror(mirrorId);
    const mirrorAclSO = await mirror.toAclSO();
    const isMirrorPublicAccess = await mirrorAclSO.isPublicAccess();
    if (!isMirrorPublicAccess) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
  }
  const { pagination, list: comments } = await record.getComments({ pageNo, pageSize });
  const activities = await Promise.all(comments.map((comment) => comment.toActivityVO()));
  return { pagination, data: activities };
}

export async function addRecordComment(user: UserSO, req: RecordAddCommentDTO) {
  const { databaseId, mirrorId, recordId, content } = req;
  const database = await DatabaseSO.init(databaseId);
  const record = await database.getRecord(recordId);
  if (mirrorId) {
    const mirror = await database.getMirror(mirrorId);
    const mirrorAclSO = await mirror.toAclSO();
    await mirrorAclSO.authorize(user, 'readNode');
  } else {
    const aclSO = await database.toAclSO();
    await aclSO.authorize(user, 'comment');
  }
  const comment = await record.addComment(user, content);
  return comment.toActivityVO();
}

export async function deleteRecordComment(
  user: UserSO,
  req: { databaseId: string; recordId: string; commentId: string },
) {
  const { databaseId, recordId, commentId } = req;
  const database = await DatabaseSO.init(databaseId);
  const record = await database.getRecord(recordId);
  const aclSO = await database.toAclSO();
  await aclSO.authorize(user, 'comment');
  const comment = await record.getComment(commentId);

  await comment.delete(user.id);
}

export async function getRecordActivities(
  ctx: ApiFetchRequestContext,
  req: RecordActivityListDTO,
): Promise<RecordActivityPaginationVO> {
  const { session } = ctx;
  const { databaseId, mirrorId, recordId, pageNo, pageSize, type } = req;
  const database = await DatabaseSO.init(databaseId);
  const record = await database.getRecord(recordId);
  let user: UserSO | undefined;
  if (session?.userId) {
    user = await UserSO.init(session.userId);
    if (mirrorId) {
      const mirror = await database.getMirror(mirrorId);
      const mirrorAclSO = await mirror.toAclSO();
      await mirrorAclSO.authorize(user, 'readNode');
    } else {
      const aclSO = await database.toAclSO();
      await aclSO.authorize(user, 'readNode');
    }
  } else {
    // 是否可公开访问
    const aclSO = await database.toAclSO();
    const isPublicAccess = await aclSO.isPublicAccess();
    if (!isPublicAccess) {
      if (!mirrorId) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }
      const mirror = await database.getMirror(mirrorId);
      const mirrorAclSO = await mirror.toAclSO();
      const isMirrorPublicAccess = await mirrorAclSO.isPublicAccess();
      if (!isMirrorPublicAccess) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }
    }
  }

  if (type === 'comment') {
    const { pagination, list: comments } = await record.getComments({ pageNo, pageSize });
    const activities = await Promise.all(comments.map((comment) => comment.toActivityVO()));
    return { pagination, data: activities };
  }
  const { pagination, list: changes } = await record.getChangeLogs({ pageNo, pageSize });
  const changeVOs = await Promise.all(changes.map((change) => change.toVO({ locale: user?.locale })));
  return {
    pagination,
    data: changeVOs,
  };
}

export async function generateImportExcelTemplate(user: UserSO, databaseId: string) {
  const database = await DatabaseSO.init(databaseId);
  // TODO: FIX: 没有删除！
  const dirPath = path.join(os.tmpdir(), generateNanoID(''));
  const localFilePath = path.join(dirPath, `${database.getName(user.locale)}_template.xlsx`);
  await fs.promises.mkdir(dirPath, { recursive: true });
  await database.generateImportExcelTemplate(localFilePath, user.locale);
  return TmpAttachmentSO.uploadLocalFileAndGetDownloadUrl(localFilePath);
}

export async function importFromTemplateExcel(user: UserSO, databaseId: string, tmpAttachmentId: string) {
  let database = await DatabaseSO.init(databaseId);

  const databaseAcl = await database.toAclSO();
  await databaseAcl.authorize(user, 'createRow');

  // Get workbook
  const tmpAttach = await TmpAttachmentSO.initById(tmpAttachmentId);
  const filePath = await tmpAttach.getObjectAsLocalFile();
  const workbook = await ExcelWorkbookWrapper.createFromFilePath(filePath);

  // 检查是否已达到用量上限
  await checkEntitlement(database, workbook.totalRows());

  // Do import from excel
  const result = await LockUtil.databaseGlobalLock(databaseId, async () => {
    // 重新获取 database，防止获取锁前，被其他请求修改了
    database = await DatabaseSO.init(databaseId);
    return database.importFromTemplateExcel(user, workbook);
  });

  // 删除本地文件, 但不删除 tmpAttachment, 因为用户可以失败重试然后重新下载
  try {
    fs.rmSync(filePath, { recursive: true });
  } catch (e) {
    Logger.error('Failed to remove tmp file', e);
  }

  return result;
}

export async function importFromTemplateExcelPreview(user: UserSO, databaseId: string, tmpAttachmentId: string) {
  const database = await DatabaseSO.init(databaseId);

  // 加载 Excel
  const tmpAttach = await TmpAttachmentSO.initById(tmpAttachmentId);
  const filePath = await tmpAttach.getObjectAsLocalFile();
  const workbook = await ExcelWorkbookWrapper.createFromFilePath(filePath);
  // 执行预览
  const result = await database.importFromTemplateExcelPreview(workbook, user.locale);

  // 删除本地文件, 但不删除 tmpAttachment, 因为用户可以失败重试然后重新下载
  try {
    fs.rmSync(filePath, { recursive: true });
  } catch (e) {
    Logger.error('Failed to remove tmp file', e);
  }

  return result;
}

export async function exportToExcel(user: UserSO, spaceId: string, databaseId: string): Promise<string> {
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const node = await space.getNode(databaseId);
  const nodeAcl = await node.toAclSO();
  await nodeAcl.authorize(member, 'exportNode');
  const database = await node.toResourceSO<DatabaseSO>();
  const dirPath = path.join(os.tmpdir(), generateNanoID(''));
  const localFilePath = path.join(dirPath, `${database.getName()}.xlsx`);
  await fs.promises.mkdir(dirPath, { recursive: true });
  const locale = user.locale;
  try {
    await database.exportToExcel(localFilePath, locale);
    const fileName = `${database.getName(locale)}.xlsx`;
    return TmpAttachmentSO.uploadLocalFileAndGetDownloadUrl(localFilePath, fileName);
  } finally {
    try {
      await fs.promises.rm(localFilePath, { recursive: true });
    } catch (e) {
      Logger.error('Failed to remove temp file', e);
    }
  }
}

export async function createFromExcel(user: UserSO, req: ExcelImportDTO) {
  const { spaceId, parentNodeId, tmpAttachmentId, fileName } = req;
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  let parentFolder: FolderSO | undefined;
  if (parentNodeId) {
    const parentNode = await space.getNode(parentNodeId);
    if (parentNode.isFolder || parentNode.isRoot) {
      parentFolder = await parentNode.toResourceSO<FolderSO>();
    } else {
      throw new ServerError(errors.node.parent_not_folder);
    }
  } else {
    parentFolder = await space.getRootFolder();
  }
  const isPrivateRootNode = parentNodeId && parentNodeId.startsWith(PRIVATE_ROOT_NODE_PREFIX);
  if (!isPrivateRootNode) {
    const parentFolderAcl = await parentFolder.toAclSO();
    await parentFolderAcl.authorize(user, 'createNode');
  }
  // return DatabaseSO.createFromExcel(user, folderSO, tmpAttachmentId, fileName);
  const tmpAttach = await TmpAttachmentSO.initById(tmpAttachmentId);
  const filePath = await tmpAttach.getObjectAsLocalFile();
  const excelName = fileName.split('/').pop()?.split('.')[0] as string;
  const workbook = await ExcelWorkbookWrapper.createFromFilePath(filePath);
  // 只导入第一个sheet
  const sheet = workbook.firstSheet();
  // const workbookRows = workbook.totalRows();
  const sheetRows = sheet.rowCount - 1; // 减去标题行

  // 1. 将 Excel 转换为 DatabaseBO
  const databaseBO = ExcelImporter.toDatabaseBO(excelName, sheet);
  // const lengthInBytes = v8.serialize(databaseBO).length;
  // const lengthInMB = lengthInBytes / (1024 * 1024);
  // console.log(`===========>length: ${lengthInMB}`);
  if (!databaseBO.fields?.length) {
    throw new Error('Excel file is empty');
  }

  // 检查用量
  const entitlement = await space.getEntitlement();
  const maxRecordsPerDatabase = entitlement.getMaxRecordsPerDatabase();
  if (maxRecordsPerDatabase !== 'Infinite' && sheetRows > maxRecordsPerDatabase) {
    const errorData: UsageExceedLimitErrorData = {
      plan: entitlement.getPlan(),
      feature: 'RECORDS_PER_DATABASE',
      max: maxRecordsPerDatabase,
      current: sheetRows,
    };
    throw new ServerError(errors.billing.usage_exceed_limit, errorData);
  }
  await entitlement.checkUsageExceed({ feature: 'RESOURCES' });
  await entitlement.checkUsageExceed({ feature: 'RECORDS_PER_SPACE', value: sheetRows });

  const importedNodeId = await parentFolder.createChildren(user, [databaseBO], {
    scope: isPrivateRootNode ? 'PRIVATE' : parentFolder.scope,
  });

  try {
    fs.rmSync(filePath, { recursive: true });
  } catch (e) {
    console.error('Failed to remove tmp file', e);
  }

  return {
    nodeId: importedNodeId,
    fields: databaseBO.fields,
    count: databaseBO.records?.length || 0,
  };
}

export async function getDoc(user: UserSO, { spaceId, docId }: { spaceId: string; docId: string }) {
  const member = await user.getMember(spaceId);
  assert(member, 'Is not a member of this space');

  const doc = await DocSO.init(docId);
  return doc.toDocVO();
}

export async function createDoc(
  user: UserSO,
  { spaceId, name, markdown }: { spaceId: string; name: string; markdown: string },
) {
  const member = await user.getMember(spaceId);
  assert(member, 'Is not a member of this space');

  const doc = await DocSO.create(user.id, spaceId, name, markdown);
  const docSO = await DocSO.init(doc.id);
  return docSO.toDocVO();
}

export async function updateDocName(
  user: UserSO,
  { spaceId, docId, name }: { spaceId: string; docId: string; name: string },
) {
  await user.getMember(spaceId);

  await DocSO.updateName(docId, name);
}
