import React from 'react';
import { z } from 'zod';
import { useLocale } from '@bika/contents/i18n/context';
import {
  DatabaseFieldType,
  DatabaseFieldProperty,
  DatabaseFieldConfigSelectOptionSchema,
  DatabaseFieldTypeSchema,
} from '@bika/types/database/bo';
import { useDatabaseVOContext } from '@bika/types/database/context';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import { useSpaceContextForce } from '@bika/types/space/context';
import { DateFormatSchema, TimeFormatSchema } from '@bika/types/system';
import { IconButton } from '@bika/ui/forms';
import MoreStandOutlined from '@bika/ui/icons/components/more_stand_outlined';
import { Dropdown, MenuButton, Menu, MenuItem } from '@bika/ui/menu';
import { Modal } from '@bika/ui/modal';
import { useSnackBar } from '@bika/ui/snackbar';

interface IHeaderFieldControlProps {
  displayName: string;
  id: string;
  fieldType: DatabaseFieldType;
  isPrimaryField: boolean;
  fieldProperty: DatabaseFieldProperty;
}

const schema = z.object({
  name: z.string().min(1, { message: 'Required' }),
  type: DatabaseFieldTypeSchema,
  options: z.array(DatabaseFieldConfigSelectOptionSchema).optional(),
  dateFormat: DateFormatSchema.optional(),
  includeTime: z.boolean().optional(),
  timeFormat: TimeFormatSchema.optional(),
});

export type FieldSubmitType = z.infer<typeof schema>;

export const HeaderFieldControl = (props: IHeaderFieldControlProps) => {
  const { displayName, id, isPrimaryField } = props;
  const spaceContext = useSpaceContextForce();
  const { useParams } = useDatabaseVOContext();
  const { databaseId: nodeId, viewId: databaseViewId } = useParams();

  const nodeApi = useNodeResourceApiContext();

  const { t } = useLocale();

  const { toast } = useSnackBar();
  const dropdownRef = React.useRef<HTMLButtonElement>(null);

  const isView = nodeId === 'view';

  const mutate = nodeApi.database.useDatabaseMutation();

  const handleDelete = async () => {
    await mutate.deleteField({
      databaseId: nodeId as string,
      fieldId: id,
    });

    toast(t.resource.remove_field_success, { variant: 'success' });
  };

  return (
    <>
      <Dropdown>
        <MenuButton slots={{ root: IconButton }} ref={dropdownRef} className="field-operation">
          <MoreStandOutlined />
        </MenuButton>
        <Menu placement="bottom-end">
          <MenuItem
            onClick={() => {
              spaceContext.showUIDrawer({
                type: 'resource-editor',
                props: {
                  screenType: 'DATABASE_FIELD',
                  fieldId: id,
                  viewId: databaseViewId as string,
                  databaseId: isView ? undefined : (nodeId as string),
                },
              });
            }}
          >
            {t.resource.edit_field}
          </MenuItem>
          {!isPrimaryField && (
            <MenuItem
              onClick={() => {
                Modal.show({
                  type: 'error',
                  title: t.resource.delete_field,
                  content: `${t.resource.delete_field_description}「${displayName}」?`,
                  okText: t.buttons.confirm,
                  cancelText: t.buttons.cancel,
                  onOk: handleDelete,
                });
              }}
            >
              {t.resource.delete_field}
            </MenuItem>
          )}
        </Menu>
      </Dropdown>
    </>
  );
};
