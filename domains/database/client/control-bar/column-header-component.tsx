// import { use<PERSON>ara<PERSON> } from 'next/navigation';
import React, { FC, useCallback, useEffect, useState } from 'react';
import { useLocale } from '@bika/contents/i18n';
import { DatabaseFieldType, DatabaseFieldProperty, DatabaseLookupField } from '@bika/types/database/bo';
import { useDatabaseVOContext } from '@bika/types/database/context';
import type { ViewFieldVO } from '@bika/types/database/vo';
import { INodePermission } from '@bika/types/node/context';
import { IconButton } from '@bika/ui/button';
import { FieldTypeIconComponent, FieldIconMapFieldType } from '@bika/ui/database/record-detail';
import type { CustomHeaderProps } from '@bika/ui/database/types';
import InfoCircleOutlined from '@bika/ui/icons/components/info_circle_outlined';
import WarnCircleFilled from '@bika/ui/icons/doc_hide_components/warn_circle_filled';
import { Box } from '@bika/ui/layouts';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip';
import { HeaderFieldControl } from './column-header-field-view';
import { useSpaceContextForce } from '../../../space/client/context/provider';

export { FieldTypeIconComponent, FieldIconMapFieldType };

export interface DatabaseHeaderProps extends CustomHeaderProps {
  fieldType: DatabaseFieldType;
  field: ViewFieldVO;
  isPrimaryField: boolean;
  fieldProperty: DatabaseFieldProperty;
  permission: INodePermission;
  disabled?: boolean;
}

const checkIsLookupField = (fieldType: DatabaseFieldType) => fieldType === 'LOOKUP';

const checkIfLookupValid = (field: DatabaseLookupField) =>
  field.property.relatedLinkFieldId && field.property.lookupTargetFieldId;

const DatabaseHeaderComponent: FC<DatabaseHeaderProps> = (props: DatabaseHeaderProps) => {
  const { isPrimaryField, displayName, column, disabled } = props;

  const { i } = useLocale();

  const [ascSort, setAscSort] = useState('inactive');
  const [descSort, setDescSort] = useState('inactive');
  const [noSort, setNoSort] = useState('inactive');

  const onSortChanged = () => {
    setAscSort(column.isSortAscending() ? 'active' : 'inactive');
    setDescSort(column.isSortDescending() ? 'active' : 'inactive');
    setNoSort(!column.isSortAscending() && !column.isSortDescending() ? 'active' : 'inactive');
  };

  const { useParams } = useDatabaseVOContext();
  const { nodeId, viewId: databaseViewId } = useParams();

  const isMirror = typeof nodeId === 'string' && nodeId.startsWith('mir');
  const isTrash = typeof window !== 'undefined' && window.location.pathname.includes('trash');

  // Create a new variable to store the edit access condition
  const hasEditAccess =
    !isMirror && !isTrash && props.permission?.privilege.privilege === 'FULL_ACCESS' && disabled !== true;

  const onSortRequested = (order: 'asc' | 'desc' | null, event: any) => {
    props.setSort(order, event.shiftKey);
  };

  const { t } = useLocale();

  useEffect(() => {
    column.addEventListener('sortChanged', onSortChanged);
    onSortChanged();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const spaceContext = useSpaceContextForce();

  const handleClick = useCallback(() => {
    if (!hasEditAccess) {
      return;
    }
    spaceContext.showUIDrawer({
      type: 'resource-editor',
      props: {
        screenType: 'DATABASE_FIELD',
        fieldId: column.getColId(),
        viewId: databaseViewId as string,
        databaseId: nodeId as string,
      },
    });
  }, [hasEditAccess, spaceContext, column, databaseViewId, nodeId]);

  let sort = null;
  if (props.enableSorting) {
    sort = (
      <div style={{ display: 'inline-block' }}>
        <div
          onClick={(event) => onSortRequested('asc', event)}
          onTouchEnd={(event) => onSortRequested('asc', event)}
          className={`customSortDownLabel ${ascSort}`}
        >
          <i className="fa fa-long-arrow-alt-down" />
        </div>
        <div
          onClick={(event) => onSortRequested('desc', event)}
          onTouchEnd={(event) => onSortRequested('desc', event)}
          className={`customSortUpLabel ${descSort}`}
        >
          <i className="fa fa-long-arrow-alt-up" />
        </div>
        <div
          onClick={(event) => onSortRequested(null, event)}
          onTouchEnd={(event) => onSortRequested(null, event)}
          className={`customSortRemoveLabel ${noSort}`}
        >
          <i className="fa fa-times" />
        </div>
      </div>
    );
  }

  const isInvalid = checkIsLookupField(props.fieldType) && !checkIfLookupValid(props.field as DatabaseLookupField);
  const isValid = !isInvalid;

  if ((props.fieldType as 'index' | DatabaseFieldType) === 'index') {
    return null;
  }

  return (
    <Box
      display="flex"
      alignItems="center"
      onDoubleClick={hasEditAccess ? handleClick : undefined}
      justifyContent="space-between"
      sx={{
        cursor: 'pointer',
        width: '100%',
      }}
    >
      <Box
        display="flex"
        onDoubleClick={hasEditAccess ? handleClick : undefined}
        sx={{
          cursor: 'pointer',
          overflowX: 'auto',
        }}
      >
        <FieldTypeIconComponent type={props.fieldType} />
        <EllipsisText>
          <Typography
            level={'h7'}
            sx={{
              color: 'var(--text-primary)',
              marginLeft: '4px',
              fontWeight: 'normal',
              fontSize: '13px',
            }}
          >
            {displayName}
          </Typography>
        </EllipsisText>

        {props.field && i(props.field.description) && isValid && (
          <Tooltip title={i(props.field.description)} variant="solid" arrow color="neutral" placement="top">
            <IconButton size={'xs'}>
              <InfoCircleOutlined color={'var(--text-secondary)'} />
            </IconButton>
          </Tooltip>
        )}
        {isInvalid && (
          <Tooltip
            title={t.editor.this_field_configuration_missing}
            variant="solid"
            arrow
            color="neutral"
            placement="top"
          >
            <IconButton size={'xs'}>
              <WarnCircleFilled color={'var(--status-warn)'} />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      {sort}
      {hasEditAccess && (
        <HeaderFieldControl
          displayName={displayName ?? t.global.action.un_named}
          id={column.getColId()}
          isPrimaryField={isPrimaryField}
          fieldType={props.fieldType}
          fieldProperty={props.fieldProperty}
        />
      )}
    </Box>
  );
};

export default DatabaseHeaderComponent;
