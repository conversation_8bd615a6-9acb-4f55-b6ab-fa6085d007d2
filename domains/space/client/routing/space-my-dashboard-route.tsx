'use client';

import dynamic from 'next/dynamic';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { DashboardVOProviderForSpaceHome } from '@bika/domains/dashboard/client/context/dashboard-vo-provider-for-space-home';
import { HeaderTitleTabs } from '@bika/domains/node/client/header/header-title-tabs-component';
import { TopRightButtons } from '@bika/domains/space/client/top-right-buttons-component';
import { WidgetRenderVO } from '@bika/types/dashboard/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import HomeOutlined from '@bika/ui/icons/components/home_outlined';
import { Divider } from '@bika/ui/layouts';
import { Skeleton } from '@bika/ui/skeleton';

const PureDashboardRenderer = dynamic(() =>
  import('@bika/domains/dashboard/client/pure-dashboard-renderer').then((module) => module.PureDashboardRenderer),
);

export default function SpaceMyHomeClientPage() {
  const locale = useLocale();
  const spaceContext = useSpaceContextForce();
  const trpcQuery = useTRPCQuery();
  const { data: homeWidgets } = trpcQuery.my.home.useQuery({
    spaceId: spaceContext?.data?.id,
  });

  const widgets: WidgetRenderVO[] | undefined = homeWidgets?.map((widget) => widget as WidgetRenderVO);

  return (
    <>
      <HeaderTitleTabs
        startDecorator={<HomeOutlined color={'var(--text-secondary)'} />}
        name={locale.t.navbar.home}
        button={
          <TopRightButtons
            onClickAdd={() => {
              spaceContext.showUIModal({ type: 'create-anything' });
            }}
          />
        }
      />
      <Divider />
      {widgets ? (
        <DashboardVOProviderForSpaceHome>
          <PureDashboardRenderer locale={locale} widgets={widgets} />
        </DashboardVOProviderForSpaceHome>
      ) : (
        <Skeleton pos="SPACE_HOME" />
      )}
    </>
  );
}
