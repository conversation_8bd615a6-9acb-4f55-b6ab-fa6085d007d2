'use client';

import dynamic from 'next/dynamic';
import React from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { HeaderTitleTabs } from '@bika/domains/node/client/header/header-title-tabs-component';
import { TopRightButtons } from '@bika/domains/space/client/top-right-buttons-component';
import { useSpaceContextForce } from '@bika/types/space/context';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button';
import type { ICalendarEvent } from '@bika/ui/components/calendar/index';
import HomeOutlined from '@bika/ui/icons/components/home_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { ModalComponent } from '@bika/ui/modal';
import { Typography } from '@bika/ui/texts';

const Calendar = dynamic(() => import('@bika/ui/components/calendar/index').then((mod) => mod.Calendar), {
  loading: () => <>Loading...</>,
  ssr: false,
});

/**
 * /space
 *
 * @param param0
 * @returns
 */
export default function MyAgendaPage() {
  const trpcQuery = useTRPCQuery();
  const spaceContext = useSpaceContextForce();
  const globalContext = useGlobalContext();
  // const aiContext = useAIContext();
  const { data: eventVOs } = trpcQuery.my.agenda.useQuery({
    spaceId: spaceContext.data.id,
  });
  const [selectedEvent, setSelectedEvent] = React.useState<ICalendarEvent | null>(null);
  const selectedAgenda = eventVOs?.find((e) => e.id === selectedEvent?.resource?.id);

  return (
    <>
      <HeaderTitleTabs
        startDecorator={<HomeOutlined color={'var(--text-secondary)'} />}
        name={'Agenda'}
        button={
          <Stack direction="row" alignItems="center" spacing={2}>
            <TopRightButtons
              onClickAdd={() => {
                spaceContext.showUIModal({ type: 'create-anything' });
              }}
            />
            <Button
              size="sm"
              sx={{
                height: '32px',
                textWrap: 'nowrap',
                paddingInline: '12px',
                borderRadius: '4px',
                color: 'var(--text-primary)',
                bgColor: 'var(--brand)',
              }}
              onClick={() => {
                // aiContext.startAIWizard({ type: 'CREATE_REMINDER' });
                globalContext.showUIModal({ name: 'AI_WIZARD', initIntent: { type: 'CREATE_REMINDER' } });
              }}
            >
              AI新建
            </Button>
          </Stack>
        }
      />

      <Box sx={{ px: 2, mt: 2 }}>
        {eventVOs ? (
          <>
            <Calendar
              events={eventVOs}
              style={{
                height: 'calc(100vh - 76px)',
                width: 'auto',
              }}
              onSelectEvent={(event) => {
                setSelectedEvent(event as ICalendarEvent);
              }}
            />
            {selectedEvent && (
              <ModalComponent onClose={() => setSelectedEvent(null)} title={'日程'} width={320}>
                <Typography level="h6">{selectedEvent.title}</Typography>
                <Typography level="body-md">开始时间: {selectedAgenda?.start}</Typography>
                <Typography level="body-md">结束时间: {selectedAgenda?.end}</Typography>
                <Typography level="body-md">{selectedAgenda?.description}</Typography>
              </ModalComponent>
            )}
          </>
        ) : (
          'Loading...'
        )}
      </Box>
    </>
  );
}
