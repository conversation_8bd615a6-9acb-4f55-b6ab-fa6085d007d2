'use client';

import { useTRPCQuery } from '@bika/api-caller/context';
import { useSpaceContextForce } from '@bika/types/space/context';
import type { OnboardingPage } from '@bika/types/space/vo';
import { Modal } from '@bika/ui/modal';
import { MembershipUpgradeComponent } from '@bika/ui/wizards/membership-upgrade-component';

export function MembershipUpgradeModal() {
  const trpcQuery = useTRPCQuery();
  const trpcUtils = trpcQuery.useUtils();
  const spaceContext = useSpaceContextForce();

  const updateSpace = trpcQuery.space.update.useMutation();

  const handleConfirm = () => {
    const newOnboardings: OnboardingPage[] = [...(spaceContext.data.settings.onboardings || []), 'INTRO_BILLING_PLAN'];

    updateSpace.mutate(
      {
        id: spaceContext.data.id,
        data: {
          settings: {
            onboardings: newOnboardings,
          },
        },
      },
      {
        onSuccess: () => {
          // 刷新 space settings
          trpcUtils.space.info.invalidate();
        },
      },
    );
  };

  const handleReferralCode = async () => {
    spaceContext.showUIModal({ type: 'space-settings', first: 'USER', tab: { type: 'USER_REFERRAL' } });
  };

  const handleNavigateMPremium = async () => {
    spaceContext.showUIModal({ type: 'space-settings', first: 'SPACE', tab: { type: 'SPACE_UPGRADE' } });
  };

  return (
    <Modal
      closable={true}
      onClose={handleConfirm}
      sx={{
        backgroundImage: `url(/assets/wizards/limit_modal_bg.png)`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        padding: '60px',
        paddingBottom: '32px',
        paddingTop: '32px',
        width: '520px',
        overflowY: 'hidden',
        borderRadius: '24px',
        '.MuiModalClose-root': {
          right: '32px !important',
          top: '32px !important',
        },
      }}
    >
      <MembershipUpgradeComponent
        handleReferralCode={handleReferralCode}
        handleNavigateMPremium={handleNavigateMPremium}
      />
    </Modal>
  );
}
