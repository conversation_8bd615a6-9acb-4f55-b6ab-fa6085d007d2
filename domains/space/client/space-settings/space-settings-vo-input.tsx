'use client';

import { <PERSON>, Tooltip } from '@mui/joy';
import React, { useState } from 'react';
import { useApiCaller } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n/context';
import type { AccessPrivilege } from '@bika/types/permission/bo';
import { useUserInfo, useSpaceContextForce } from '@bika/types/space/context';
import type { SpaceSettingsVO } from '@bika/types/space/vo';
import type { iString } from '@bika/types/system';
import { Button } from '@bika/ui/button';
import { PreviewShape, ImageCropUpload } from '@bika/ui/components/image-crop-upload/index';
import { FormHelperText, Input } from '@bika/ui/forms';
import ImageOutlined from '@bika/ui/icons/components/image_outlined';
import QuestionCircleOutlined from '@bika/ui/icons/components/question_circle_outlined';
import { Modal } from '@bika/ui/modal-component';
import { BooleanInput } from '@bika/ui/shared/types-form/boolean-input';
import { MarkdownStringInput } from '@bika/ui/shared/types-form/markdown-string-input';
import { StringArrayInput } from '@bika/ui/shared/types-form/string-array-input';
import { useSnackBar } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/text-components';
import { PermissionSelect } from '../../../node/client/permission-select';
import { useSpaceId } from '../context';

interface Props {
  value: SpaceSettingsVO;
  onChange: (value: SpaceSettingsVO) => void;
}

const presetPhotos = [
  { url: '/assets/home/<USER>' },
  { url: '/assets/home/<USER>' },
  { url: '/assets/home/<USER>' },
  { url: '/assets/home/<USER>' },
  { url: '/assets/home/<USER>' },
];

export function SpaceSettingsVOInput(props: Props) {
  const locale = useLocale();
  const { t, lang } = locale;
  const spaceId = useSpaceId();
  const userInfo = useUserInfo();
  const space = useSpaceContextForce();
  const { toast } = useSnackBar();

  const { trpcQuery } = useApiCaller();

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isLegalSpaceId, setIsLegalSpaceId] = useState(false);
  const [showImageUpload, setShowImageUpload] = useState(false);

  const updateSpace = trpcQuery.space.update.useMutation();
  const deleteSpace = trpcQuery.space.delete.useMutation();

  const onDeleteSpace = () => {
    deleteSpace.mutate(
      {
        id: spaceId,
      },
      {
        onSuccess: () => {
          toast(t.settings.space.delete_space_success, {
            variant: 'success',
          });
          window.location.reload();
        },
      },
    );
  };

  return (
    <>
      <div>
        <Typography level="b3" textColor="var(--text-primary)" mb="4px">
          {t.space.announcement}
        </Typography>
        <FormHelperText>{t.settings.space.announcement_description}</FormHelperText>
        <MarkdownStringInput
          locale={locale}
          isMultiLang
          label={' '}
          value={props.value.announcement || ''}
          onChange={(newVal: iString) => {
            if (typeof props.value.announcement === 'string') {
              props.onChange({ ...props.value, announcement: { [lang]: newVal } });
            } else {
              props.onChange({ ...props.value, announcement: newVal });
            }
          }}
          height="120px"
        />
      </div>
      <div>
        <Typography level="b3" textColor="var(--text-primary)" mb="4px">
          {t.settings.space.wallpaper_title}
        </Typography>
        <FormHelperText>{t.settings.space.wallpaper_description}</FormHelperText>
        <Button variant="soft" color="neutral" sx={{ mt: '8px', gap: 0.5 }} onClick={() => setShowImageUpload(true)}>
          <ImageOutlined color="var(--text-secondary)" size={16} />
          {t.settings.space.wallpaper_button}
        </Button>

        {showImageUpload && (
          <ImageCropUpload
            onClose={() => {
              setShowImageUpload(false);
            }}
            confirm={async (data) => {
              // setValue((prev) => ({ ...prev, avatar: data }));
              await updateSpace.mutateAsync({
                id: space.data.id,
                data: {
                  settings: {
                    wallpaper: data,
                  },
                },
              });
              space.refetch();
              setShowImageUpload(false);
            }}
            allowTab={['PRESET']}
            target="BgImage"
            config={{
              preset: {
                presetPhotos,
              },
            }}
            avatarName={' '}
            // avatarPrefix="avatar"
            previewShape={PreviewShape.Square}
            // type={'PRESET'}
            value={undefined}
            // initPreview={null}
          />
        )}
      </div>
      <div>
        <Typography level="b3" textColor="var(--text-primary)" mb="4px">
          {t.settings.space.watermark_description}
        </Typography>
        <FormHelperText>{t.settings.space.watermark_description_2}</FormHelperText>

        <div style={{ display: 'flex', marginTop: '8px', alignItems: 'center' }}>
          <BooleanInput
            label={t.space.show_watermark}
            value={props.value.watermark || false}
            onChange={(newVal) => {
              props.onChange({ ...props.value, watermark: newVal! });
            }}
          />
        </div>
      </div>

      <div>
        <Typography level="b3" textColor="var(--text-primary)" mb="4px">
          {t.settings.space.authorized_domain_settings}
        </Typography>
        <FormHelperText>{t.settings.space.authorized_domain_description}</FormHelperText>
        {/* 企业邮箱管理 */}
        <StringArrayInput
          // label={t.settings.space.authorized_domain}
          placeholder={'example.com'}
          value={props.value.allowEmailDomains || []}
          onChange={(newVal) => {
            props.onChange({ ...props.value, allowEmailDomains: newVal });
          }}
          delete={(curIndex) => {
            props.onChange({
              ...props.value,
              allowEmailDomains: props.value.allowEmailDomains?.filter((_, index) => index !== curIndex),
            });
          }}
        />
      </div>

      <div>
        <Typography level="b3" textColor="var(--text-primary)" mb="4px">
          {t.settings.space.permission_settings_description}
        </Typography>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <BooleanInput
            label={t.settings.space.disable_global_manage_resource}
            value={!!props.value.resourceGlobalDefaultPermission}
            onChange={(newVal) => {
              const value =
                newVal && !props.value.resourceGlobalDefaultPermission
                  ? { ...props.value, resourceGlobalDefaultPermission: 'FULL_ACCESS' as AccessPrivilege }
                  : { ...props.value, resourceGlobalDefaultPermission: null };
              props.onChange(value);
            }}
            sx={{ marginTop: '8px', marginRight: '4px' }}
          />
          <Tooltip
            title={t.settings.space.disable_global_manage_resource_learn_more}
            variant="solid"
            arrow
            color="neutral"
            placement="top"
          >
            <Link
              href="/help/guide/space/space-management"
              target="_blank"
              rel="noreferrer"
              endDecorator={<QuestionCircleOutlined color="var(--text-secondary)" />}
              sx={{ marginTop: '8px' }}
            />
          </Tooltip>
        </div>

        {/* 屏蔽Global 资源管理，安全设置 */}
        {props.value.resourceGlobalDefaultPermission && (
          <div style={{ maxWidth: '240px', marginTop: '8px' }}>
            <PermissionSelect
              currentPrivilege={props.value.resourceGlobalDefaultPermission}
              onChangePrivilege={(value: AccessPrivilege) => {
                props.onChange({ ...props.value, resourceGlobalDefaultPermission: value });
              }}
              onRemovePrivileage={() => {
                props.onChange({ ...props.value, resourceGlobalDefaultPermission: null });
              }}
              listboxPlacement="top-start"
              hideRemoveButton
              slotProps={{
                trigger: {
                  sx: {
                    border: '1px solid transparent !important',
                    backgroundColor: 'var(--bg-controls) !important',
                    boxShadow: 'none',
                    color: 'var(--text-primary)',
                    '&:hover': {
                      border: '1px solid var(--border-default)!important',
                    },
                  },
                },
              }}
            />
          </div>
        )}
      </div>
      {userInfo?.isSpaceOwner && (
        <div>
          <Typography level="b3" textColor="var(--text-primary)" mb="4px">
            {t.settings.space.delete_space}
          </Typography>
          <FormHelperText>{t.settings.space.delete_space_desc}</FormHelperText>

          <Button
            color="danger"
            variant="soft"
            onClick={() => {
              setShowDeleteModal(true);
            }}
            sx={{ marginTop: '8px' }}
          >
            {t.settings.space.delete_space}
          </Button>
        </div>
      )}
      {showDeleteModal && (
        <Modal
          width={500}
          onClose={() => {
            setIsLegalSpaceId(false);
            setShowDeleteModal(false);
          }}
        >
          <div className="text-h6 text-[--text-primary]">{t.settings.space.delete_space_confirm_title}</div>
          <div>
            {t('settings.space.delete_space_confirm_desc', {
              spaceId: <span className={'text-[--brand]'}>{spaceId}</span>,
            })}
          </div>
          <Input
            placeholder={t.settings.space.please_input_space_id}
            onChange={(e) => {
              if (e.target.value === spaceId) {
                setIsLegalSpaceId(true);
                return;
              }
              setIsLegalSpaceId(false);
            }}
          />
          <div className="flex justify-end mt-8 space-x-2">
            <Button
              color="neutral"
              variant="soft"
              onClick={() => {
                setIsLegalSpaceId(false);
                setShowDeleteModal(false);
              }}
            >
              {t.action.cancel}
            </Button>
            <Button
              color="danger"
              variant="soft"
              loading={deleteSpace.isPending}
              onClick={onDeleteSpace}
              disabled={!isLegalSpaceId}
            >
              {t.settings.space.delete_space}
            </Button>
          </div>
        </Modal>
      )}
    </>
  );
}
