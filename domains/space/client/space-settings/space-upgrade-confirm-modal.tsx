import { selectClasses } from '@mui/joy/Select';
import dayjs from 'dayjs';
import React, { useMemo, useState, useRef, useEffect } from 'react';
import { capitalize } from 'string-ts';
import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { utils } from '@bika/domains/shared/client';
import { BillingAction, ISOCurrencies, ISOCurrency, SpacePlanType, SpacePlanTypes } from '@bika/types/pricing/bo';
import { Rewardful } from '@bika/types/pricing/dto';
import { PayInterval } from '@bika/types/pricing/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button';
import { Option, Select } from '@bika/ui/forms';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import SelectMarkFilled from '@bika/ui/icons/doc_hide_components/select_mark_filled';
import { ModalComponent } from '@bika/ui/modal';
import { useSnackBar } from '@bika/ui/snackbar';
import { PaymentStatus } from './components/payment-status';
import { PriceItem } from './components/price-item';

// 参考https://hk.godaddy.com/，底部切换货币选择器，货币文字显示
const currencyCodeToTextMap: { [_cur in ISOCurrency]: string } = {
  USD: '🇺🇸 USD $',
  CNY: '🇨🇳 CNY ¥',
  HKD: '🇭🇰 HKD HK$',
  TWD: '🇹🇼 TWD NT$',
  JPY: '🇯🇵 JPY ¥',
  MOP: '🇲🇴 MOP MOP$',
  SGD: '🇸🇬 SGD SG$',
  CAD: '🇨🇦 CAD CA$',
  EUR: '🇪🇺 EUR €',
  AUD: '🇦🇺 AUD A$',
  KRW: '🇰🇷 KRW ₩',
};

const textStyle = 'text-[--text-primary] underline decoration-solid underline-offset-4 cursor-pointer	';
const divStyle = 'flex justify-between	items-center';

type IPeyIntervalCardProps = {
  payInterval: PayInterval;
  onClick: () => void;
  isActive?: boolean;
};

const PeyIntervalCard: React.FC<IPeyIntervalCardProps> = ({ payInterval, isActive, onClick }) => {
  const { t } = useLocale();

  const title = payInterval === 'month' ? t.settings.upgrade.pay_monthly : t.settings.upgrade.pay_annual;

  return (
    <div
      className={utils.cn(
        'border-[1px] border-[--border-default] rounded py-[10px] px-[16px] cursor-pointer relative',
        isActive && 'bg-[--rainbow-yellow1]',
      )}
      onClick={onClick}
    >
      <div className={'text-b1'}>{title}</div>

      {isActive && (
        <SelectMarkFilled color={'var(--rainbow-yellow4)'} className={'absolute -bottom-[1px] -right-[1px]'} />
      )}
    </div>
  );
};

type Props = {
  onClose: (done: boolean) => void;
  plan: SpacePlanType;
  payInterval: PayInterval;
  currencyCode: ISOCurrency;
};

export function SpaceUpgradeConfirmModal(props: Props) {
  const _globalContext = useGlobalContext();
  const { lang, t } = useLocale();
  const { toast } = useSnackBar();
  const spaceContext = useSpaceContextForce();
  const { trpc, trpcQuery } = useApiCaller();
  const spaceId = spaceContext.data.id;
  // const pathname = usePathname();

  // 当前订阅
  const subscription = spaceContext.data?.subscription;

  const isLockedPlan = Boolean(props.plan);
  const isLockedInterval = Boolean(props.payInterval);
  const isLockedCurrency = Boolean(props.currencyCode);

  const [plan, setPlan] = useState<SpacePlanType>(props.plan);
  const [payInterval, setPayInterval] = useState<PayInterval>(props.payInterval);
  // const [withBKC] = useState<boolean>(false);
  const [currencyCode, setCurrencyCode] = useState<ISOCurrency>(props.currencyCode);
  const [payStatus, setPayStatus] = useState<'success' | 'fail' | 'pending' | null>(null);

  const [expandCheckoutDetail, setExpandCheckoutDetail] = useState(false);
  const [paying, setPaying] = useState(false);

  const timerRef = useRef<number | null>(null);

  const checkPaymentStatus = async (paymentId: string, timer: number) => {
    try {
      const data = await trpc.billing.paymentInfo.query({
        paymentId,
      });
      if (data.status === 'PENDING') {
        return;
      }
      // 相关的状态可以查看 PaymentStatuses
      if (data.status === 'SUCCESS') {
        window.location.href = `/space/${spaceId}?popup=true&plan=${plan}`;
      } else {
        setPayStatus('fail');
      }
      clearInterval(timer);
    } catch (_e) {
      setPayStatus('fail');
      clearInterval(timer);
    }
  };

  // const { data: coinsWallet, isLoading: isLoadingCoins } = trpcQuery.user.coins.useQuery();
  const { data: checkoutPreview, isLoading: isLoadingCheckoutPreview } = trpcQuery.billing.checkoutPreview.useQuery(
    {
      spaceId,
      plan,
      interval: payInterval,
      currency: currencyCode,
    },
    { retry: false },
  );
  const createCheckout = trpcQuery.billing.createCheckout.useMutation();
  const updateSubscription = trpcQuery.billing.updateSubscription.useMutation();

  // 货币格式化字符串，根据语言、货币种类而变化
  const amountFormat = useMemo(() => {
    const formatter = new Intl.NumberFormat(lang, { style: 'currency', currency: currencyCode });
    return (n: number = 0) => formatter.format(n / 100);
  }, [lang, currencyCode]);

  useEffect(() => {
    if (timerRef.current && payStatus !== 'pending') {
      clearInterval(timerRef.current);
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [payStatus]);

  const getRewardful = (): Rewardful | undefined => {
    if (window.Rewardful) {
      // console.log('referral', window.Rewardful.referral);
      // console.log('coupon', window.Rewardful.coupon);
      return {
        referral: window.Rewardful.referral || undefined,
        coupon: (window.Rewardful.coupon && window.Rewardful.coupon.id) || undefined,
      };
    }
    return undefined;
  };

  const gotoPay = (action?: BillingAction) => {
    if (!action) {
      toast('error action', { variant: 'error' });
      return;
    }

    setPayStatus('pending');

    if (action === 'CREATE') {
      // 创建订阅, 去付款页面
      const rewardful = getRewardful();
      createCheckout.mutate(
        {
          spaceId,
          plan,
          interval: payInterval,
          currency: currencyCode,
          rewardful,
        },
        {
          onSuccess: (data) => {
            window.open(data.checkoutSessionUrl);
            timerRef.current = setInterval(() => {
              checkPaymentStatus(data.paymentId, timerRef.current!);
            }, 3000) as unknown as number;
          },
          onError: (_e) => {
            setPayStatus('fail');
          },
        },
      );
    } else {
      // 更新订阅, 直接更新
      setPaying(true);
      updateSubscription.mutate(
        {
          spaceId,
          plan,
          interval: payInterval,
        },
        {
          onSuccess: (_data) => {
            toast('Update Subscription Successfully', { variant: 'success' });
            setPaying(false);
            props.onClose(true);
            window.location.reload();
          },
          onError: (_e) => {
            setPaying(false);
          },
        },
      );
    }
  };

  const formatDate = (unix: number) => dayjs.unix(unix).format('LL');

  return (
    <>
      <ModalComponent
        onClose={() => {
          props.onClose(false);
        }}
        title={<div className={'text-center text-h6'}>{t.settings.coin_rewards.upgrade_space}</div>}
      >
        <div className={'flex pt-[12px]'}>
          <div className={'space-y-[36px] flex-shrink-0'}>
            {/* 左边区域 */}
            {/* 当前空间站名称 */}
            <PriceItem title={t.settings.upgrade.space}>{spaceContext.data.name}</PriceItem>
            {/* 当前计划 */}
            <PriceItem title={t.settings.billing.current_plan}>
              {capitalize(String(subscription?.planName).toLowerCase())}
            </PriceItem>
            {/* 当前空间站成员数 */}
            <PriceItem title={t.pricing.seat}>{checkoutPreview?.quantity}</PriceItem>
            {/* 周期账单日 */}
            <PriceItem title={t.settings.billing.next_invoice_date}>
              {checkoutPreview?.period && formatDate(checkoutPreview.period.end)}
            </PriceItem>
            {/* 货币选择 */}
            <PriceItem title={t.settings.upgrade.currency}>
              <Select
                defaultValue={currencyCode as unknown as string}
                value={currencyCode}
                disabled={isLockedCurrency}
                indicator={<ChevronDownOutlined />}
                sx={{
                  width: '180px',
                  height: '40px',
                  [`&.${selectClasses.root}`]: {
                    background: 'var(--bg-controls)',
                    boxShadow: 'none',
                    // background: 'red !important',
                  },
                }}
                onChange={(_, newCurrencyCode) => {
                  setCurrencyCode(newCurrencyCode as unknown as ISOCurrency);
                }}
              >
                {ISOCurrencies.map((curCode: ISOCurrency, i: number) => (
                  <Option key={i} value={curCode}>
                    {currencyCodeToTextMap[curCode]}
                  </Option>
                ))}
              </Select>
            </PriceItem>
            {/* 订阅计划选择 */}
            <PriceItem title={t.settings.upgrade.plan}>
              <Select
                defaultValue={plan as unknown as string}
                value={plan}
                indicator={<ChevronDownOutlined />}
                onChange={(_, newPlan) => {
                  setPlan(newPlan as unknown as SpacePlanType);
                }}
                disabled={isLockedPlan}
                sx={{
                  width: '180px',
                  height: '40px',
                  [`&.${selectClasses.root}`]: {
                    background: 'var(--bg-controls)',
                    boxShadow: 'none',
                    // background: 'red !important',
                  },
                }}
              >
                {SpacePlanTypes.map((option) => (
                  <Option key={option} value={option}>
                    {capitalize(String(option).toLowerCase())}
                  </Option>
                ))}
              </Select>
            </PriceItem>
            {/* 订阅周期选择 */}
            <PriceItem title={t.settings.upgrade.cycle}>
              <div className={'flex items-center space-x-6'}>
                {(!isLockedInterval || (isLockedInterval && payInterval === 'month')) && (
                  <PeyIntervalCard
                    payInterval={'month'}
                    isActive={payInterval === 'month'}
                    onClick={() => setPayInterval('month')}
                  />
                )}
                {(!isLockedInterval || (isLockedInterval && payInterval === 'year')) && (
                  <PeyIntervalCard
                    payInterval={'year'}
                    isActive={payInterval === 'year'}
                    onClick={() => setPayInterval('year')}
                  />
                )}
              </div>
            </PriceItem>
            {/* 支付方式选择 */}
            {/* <PriceItem title={t.settings.upgrade.payment}>
              <div
                className={utils.cn(
                  'border-[1px] border-[--border-default] rounded w-[180px] h-[44px] flex justify-center	items-center cursor-pointer relative overflow-hidden',
                  'bg-[--rainbow-yellow1]',
                )}
              >
                <span className={'text-h6'}>Stripe</span>
                <SelectMarkFilled color={'var(--rainbow-yellow4)'} className={'absolute -bottom-[1px] -right-[1px]'} />
              </div>
            </PriceItem> */}
            {/* <div className={'!mt-4 pl-[150px]'}>
              <div className={'flex items-center space-x-2 mb-2'}>
                <Checkbox
                  onChange={(e) => {
                    setWithBKC(e.target.checked);
                  }}
                ></Checkbox>
                <div>
                  {' '}
                  <span className={'text-b3'}>{t.settings.upgrade.bkc}</span>
                  <span className={'text-b3 text-[--text-secondary]'}>
                    {'('}
                    {t.settings.upgrade.currently_owns}{' '}
                    <span className={'text-[--rainbow-yellow5] mx-2'}>{coinsWallet?.credit || 0}</span>
                    {')'}
                  </span>
                </div>
              </div>

              <div className={'text-[--text-secondary]'}>
                {t.settings.upgrade.other_method_bkc}{' '}
                <span className={textStyle}>{t.settings.upgrade.invite_people}</span>
                {t.settings.upgrade.or} <span className={textStyle}>{t.settings.upgrade.invite_space}</span>{' '}
                {t.settings.upgrade.get_more_bika}
              </div>
            </div> */}
          </div>
          <div className={'w-full ml-[53px] flex items-end h-[520px]'}>
            <div className={'bg-[--bg-popup] w-full p-4 rounded-lg'}>
              {isLoadingCheckoutPreview ? (
                <div>{t.settings.upgrade.loading_tips}</div>
              ) : (
                <>
                  {/* 控制显示的部分 */}
                  {expandCheckoutDetail && (
                    <div className={'space-y-2 mb-2 pl-[14px]'}>
                      <div className={divStyle}>
                        <div className={'text-b3'}>{t.settings.upgrade.quanty}</div>
                        <div className={'text-b3'}>{checkoutPreview?.quantity}</div>
                      </div>
                      <div className={divStyle}>
                        <div className={'text-b3'}>{t.settings.upgrade.currency}</div>
                        <div className={'text-b3'}>{currencyCodeToTextMap[checkoutPreview?.currencyCode || 'USD']}</div>
                      </div>
                      <div className={divStyle}>
                        <div className={'text-b3'}>{t.settings.upgrade.unit_price}</div>
                        <div className={'text-b3'}>{amountFormat(checkoutPreview?.amountPerQuantity)}</div>
                      </div>
                      <div className={'bg-[--border-default] w-full h-[1px]'}></div>
                      <div className={divStyle}>
                        <div className={'text-b3'}>Fee Detail</div>
                        <div className={'text-b3'}>Amount</div>
                      </div>
                      {checkoutPreview?.details?.map((item) => (
                        <>
                          <div className={divStyle}>
                            <div className={'text-b3'}>
                              {`${formatDate(item.period.start)} - ${formatDate(item.period.end)}`}
                            </div>
                          </div>
                          <div className={divStyle}>
                            <div className={'text-b3'}>{`(${item.description}) `}</div>
                            <div className={'text-b3'}>{`${amountFormat(item.amount)}`}</div>
                          </div>
                        </>
                      ))}
                      <div className={'bg-[--border-default] w-full h-[1px]'}></div>
                      <div className={divStyle}>
                        <div className={'text-b3'}>{t.settings.upgrade.subtotal}</div>
                        <div className={'text-b3'}>{amountFormat(checkoutPreview?.originalAmount)}</div>
                      </div>
                      {/* <div className={divStyle}>
                        <div className={'text-b3'}>{t.settings.upgrade.bkc_deduction}</div>
                        <div className={'text-b3 text-[--rainbow-tangerine5]'}>{price?.byCoins}</div>
                      </div> */}
                      <div className={divStyle}>
                        <div className={'text-b3'}>{t.settings.upgrade.total}</div>
                        <div className={'text-b3'}>{amountFormat(checkoutPreview?.amount)}</div>
                      </div>
                    </div>
                  )}
                  {/* 保持显示的部分 */}
                  <div>
                    <div className={'flex items-center justify-between'}>
                      <div
                        className={'text-[--rainbow-tangerine5] text-[32px] leading-8 font-normal flex items-center'}
                        style={{
                          fontFamily: 'Bebas Neue',
                        }}
                      >
                        {amountFormat(checkoutPreview?.amount)}
                      </div>
                      <div
                        className={'flex items-center cursor-pointer'}
                        onClick={() => setExpandCheckoutDetail((pre) => !pre)}
                      >
                        {t.settings.upgrade.detail}
                        <ChevronDownOutlined className={utils.cn('ml-1', expandCheckoutDetail && 'rotate-180')} />
                      </div>
                    </div>
                    <div className={'mt-[10px]'}>
                      {t.settings.upgrade.cycle_descritpion}
                      <span className={textStyle}>{t.settings.upgrade.benefit_details}</span>
                    </div>
                    <Button
                      color="primary"
                      sx={{ width: '100%', margin: '10px 0' }}
                      disabled={paying}
                      onClick={() => gotoPay(checkoutPreview?.action)}
                    >
                      {t.settings.upgrade.pay_now}
                    </Button>
                    <div className={'text-b4'}>{t.settings.upgrade.pay_tips}</div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        {payStatus && <PaymentStatus status={payStatus} onClose={() => setPayStatus(null)} plan={plan} />}
      </ModalComponent>
    </>
  );
}
