import { But<PERSON> } from '@mui/joy';
import React from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useSpaceContextForce } from '@bika/types/space/context';
import { StringInput } from '@bika/ui/shared/types-form/string-input';
import { useSnackBar } from '@bika/ui/snackbar/snackbar-component';

export function DeleteSpaceModal() {
  const space = useSpaceContextForce();
  const { toast } = useSnackBar();
  const trpcQuery = useTRPCQuery();

  const [spaceId, setSpaceId] = React.useState('');
  const deleteSpace = trpcQuery.space.delete.useMutation();

  return (
    <>
      Are you confirm to delete this space?
      <StringInput value={spaceId} onChange={setSpaceId} />
      <Button
        disabled={deleteSpace.isPending}
        onClick={() => {
          if (spaceId !== space.data.id) {
            toast('Not Matching Space ID', { variant: 'error' });
          } else {
            deleteSpace.mutate({ id: spaceId });
          }
        }}
      >
        DELETE
      </Button>
    </>
  );
}
