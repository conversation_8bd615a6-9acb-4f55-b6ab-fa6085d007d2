import { z } from 'zod';
import { Space<PERSON> } from '@bika/domains/space/server/space-so';
import { SpaceAuditLogSO } from '@bika/domains/system/server/audit/space-audit-log-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-orm/trpc';
import {
  SpaceCreateSchema,
  SpaceAuditLogListSchema,
  SpaceInfoSchema,
  SpaceListSchema,
  SpaceUpdateSchema,
  SpaceInstallTemplateSchema,
  SpaceCreditTransactionListSchema,
} from '@bika/types/space/dto';
import { SpaceCreditVOSchema } from '@bika/types/space/vo';
import * as SpaceController from './space-controller';

export const spaceRouter = router({
  /**
   * list space
   */
  list: protectedProcedure.input(SpaceListSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return SpaceController.list(user, input);
  }),

  /**
   * Retrieve space info
   */
  info: protectedProcedure.input(SpaceInfoSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return SpaceController.info(user, input);
  }),

  /**
   * New a Space
   */
  create: protectedProcedure.input(SpaceCreateSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const space = await user.createSpace(input);
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'space.create',
      name: user.name,
    });
    const spaceVO = await space.toVO();
    return spaceVO;
  }),

  /**
   * Update space info
   */
  update: protectedProcedure.input(SpaceUpdateSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const spaceVO = await SpaceController.update(user, input);
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: spaceVO.id,
      type: 'space.update',
    });
  }),

  /**
   * 删除空间站
   */
  delete: protectedProcedure.input(z.object({ id: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { id } = input;
    const { userId } = ctx.session!;

    const user = await UserSO.init(userId);
    const space = await SpaceSO.init(id);
    await space.delete(user);
  }),

  /**
   * 安装模板
   */
  installTemplate: protectedProcedure.input(SpaceInstallTemplateSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const templateFolder = await SpaceController.installTemplate(user, input);
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: templateFolder.spaceId,
      type: 'template.install',
      id: templateFolder.templateId,
      name: JSON.stringify(templateFolder.name),
      spaceid: templateFolder.spaceId,
      nodeid: templateFolder.id,
    });
    return templateFolder.id;
  }),

  /**
   * TODO 待迁移
   */
  getIntegrationAuthorizationUrl: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        integrationId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return SpaceController.getIntegrationAuthorizationUrl(user, input.spaceId, input.integrationId);
    }),

  /**
   * space空间站审计日志，space settings里用，有数量限制
   */
  auditLogs: protectedProcedure.input(SpaceAuditLogListSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const { spaceId, ip, pageNo, pageSize } = input;

    const user = await UserSO.init(userId);
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.getAdminRoleAclSO().authorize(member, 'readAuditLog');

    const entitlement = await space.getEntitlement();
    const { startDate, endDate } = entitlement.planFeature.getDateFeature('SPACE_AUDIT_LOGS');

    const result = await SpaceAuditLogSO.list(ctx.locale, { spaceId, startDate, endDate, ip }, { pageNo, pageSize });

    return {
      pagination: result.pagination,
      data: result.list,
    };
  }),

  /**
   * 获取用户最后活跃的空间
   */
  getLastActiveSpace: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const { userId } = ctx.session!;
    return SpaceController.getUserLastActiveSpace(userId);
  }),

  /**
   * 获取空间站积分余额
   */
  getCredit: protectedProcedure
    .input(z.object({ spaceId: z.string() }))
    .output(SpaceCreditVOSchema)
    .query(async (opts) => {
      const { input } = opts;
      const { spaceId } = input;
      return SpaceController.getCredit(spaceId);
    }),

  getCreditTransaction: protectedProcedure.input(SpaceCreditTransactionListSchema).query(async (opts) => {
    const { input } = opts;
    const { spaceId, pageNo, pageSize } = input;
    const space = await SpaceSO.init(spaceId);
    const coinAccount = await space.billing.getCoinsAccount();
    return coinAccount.getTransactionsWithPagination({ page: { pageNo, pageSize } });
  }),

  getRootNode: protectedProcedure.input(z.object({ spaceId: z.string() })).query(async (opts) => {
    const { input } = opts;
    const { spaceId } = input;
    const space = await SpaceSO.init(spaceId);
    const rootNode = await space.getRootFolder();
    return rootNode.toVO({
      userId: opts.ctx.session?.userId,
      locale: opts.ctx.locale,
      depth: 0,
    });
  }),
});
