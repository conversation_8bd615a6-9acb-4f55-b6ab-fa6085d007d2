import React, { useState } from 'react';
import { useApiCaller } from '@bika/api-caller';
import { LicenseCode } from '@bika/domains/admin/types/bo';
import {
  CrudDatabaseManager,
  CrudDatabaseManagerRefHandle,
} from '@bika/domains/database/client/database/crud-database-manager';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button';
import type { ListGridRefHandle } from '@bika/ui/components/list-grid/types';
import { CheckboxInput } from '@bika/ui/shared/types-form/checkbox-input';
import { DatetimeInput } from '@bika/ui/shared/types-form/datetime-input';
import { StringInput } from '@bika/ui/shared/types-form/string-input';
import { useSnackBar } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/website/typography/index';

interface InputProps {
  value: LicenseCode;
  onChange: (value: LicenseCode) => void;
}

const CodeBOInput: React.FC<InputProps> = (props: InputProps) => (
  <div>
    <StringInput
      label="Name"
      type="string"
      required={true}
      value={props.value.name}
      onChange={(newVal) => {
        props.onChange({
          ...props.value,
          name: newVal,
        });
      }}
    />
    <StringInput
      label="Seats"
      type="number"
      required={true}
      value={props.value.seat}
      onChange={(newVal) => {
        const val = newVal ? parseInt(newVal.toString(), 10) : 10;
        props.onChange({
          ...props.value,
          seat: val,
        });
      }}
    />
    <StringInput label="Plan" type="string" readOnly={true} value={props.value.plan} onChange={() => {}} />
    <CheckboxInput
      label="Trial"
      value={props.value.trial ?? false}
      onChange={(newVal) => {
        props.onChange({
          ...props.value,
          trial: newVal,
        });
      }}
    />
    <CheckboxInput
      label="Offline"
      value={props.value.offline ?? false}
      onChange={(newVal) => {
        props.onChange({
          ...props.value,
          offline: newVal,
        });
      }}
    />
    <DatetimeInput
      label="Expired At"
      required={true}
      options={{
        timeZone: 'Asia/Shanghai',
        includeTime: false,
      }}
      value={props.value.expiredAt}
      onChange={(newVal) => {
        props.onChange({
          ...props.value,
          expiredAt: newVal,
        });
      }}
    />
    <StringInput
      label="Remark"
      type="string"
      required={false}
      value={props.value.remark}
      onChange={(newVal) => {
        props.onChange({
          ...props.value,
          remark: newVal,
        });
      }}
    />
  </div>
);

interface IAddModalProps {
  onClose: () => void;
}

const AddModal: React.FC<IAddModalProps> = ({ onClose }) => {
  const { trpcQuery } = useApiCaller();
  const globalContext = useGlobalContext();
  const { toast } = useSnackBar();
  const listGridRef = React.useRef<ListGridRefHandle>(null);
  const [codeBO, setCodeBO] = useState<LicenseCode>({
    name: '',
    expiredAt: '',
    remark: '',
    seat: -1,
    plan: 'ENTERPRISE_SELF_HOSTED',
    trial: false,
    offline: false,
    advancedAI: false,
    createdBy: globalContext.authContext.me?.user.id || '(ERROR)',
    createdAt: new Date().toISOString(),
  });

  const { mutateAsync: createLicenseCode } = trpcQuery.admin.licenseCode.create.useMutation();

  return (
    <>
      <Typography level={3}>创建授权码</Typography>
      <CodeBOInput value={codeBO} onChange={(value) => setCodeBO(value)} />
      <div className="flex justify-center w-full mt-6">
        <Button
          onClick={async () => {
            // console.log('codeBO', codeBO);
            await createLicenseCode(codeBO);
            listGridRef.current?.refresh();
            toast('创建成功');
            onClose();
          }}
        >
          创建
        </Button>
      </div>
    </>
  );
};

interface LicenseCodeUpdateProps {
  recordDetail: any;
  closeModal: () => void;
}

const LicenseCodeUpdate: React.FC<LicenseCodeUpdateProps> = ({ recordDetail, closeModal }) => {
  const { trpcQuery } = useApiCaller();
  const { toast } = useSnackBar();
  const [codeBO, setCodeBO] = useState<LicenseCode>({
    ...recordDetail.record.data,
    name: recordDetail.record.cells.name.value,
    expiredAt: recordDetail.record.cells.expiredAt.value,
    remark: recordDetail.record.cells.remark.value,
    seat: recordDetail.record.cells.seat?.value || -1,
    plan: recordDetail.record.cells.plan?.value || 'ENTERPRISE_SELF_HOSTED',
    trial: recordDetail.record.cells.trial?.value || false,
    offline: recordDetail.record.cells.offline?.value || false,
    advancedAI: recordDetail.record.cells.advancedAI?.value || false,
  });
  const { mutateAsync: updateLicenseCode } = trpcQuery.admin.licenseCode.update.useMutation();
  return (
    <>
      <Typography level={3}>更新授权码</Typography>
      <div>
        <StringInput label="Name" type="string" readOnly={true} value={codeBO.name} onChange={() => {}} />
        <StringInput label="Seats" type="number" readOnly={true} value={codeBO.seat} onChange={() => {}} />
        <StringInput label="Plan" type="string" readOnly={true} value={codeBO.plan} onChange={() => {}} />
        <CheckboxInput label="Trial" value={codeBO.trial ?? false} onChange={() => {}} />
        <CheckboxInput label="Offline" value={codeBO.offline ?? false} onChange={() => {}} />
        <DatetimeInput
          label="Expired At"
          required={true}
          options={{
            timeZone: 'Asia/Shanghai',
            includeTime: false,
          }}
          value={codeBO.expiredAt}
          onChange={(newVal) => {
            setCodeBO({
              ...codeBO,
              expiredAt: newVal,
            });
          }}
        />
        <StringInput
          label="Remark"
          type="string"
          required={false}
          value={codeBO.remark}
          onChange={(newVal) => {
            setCodeBO({
              ...codeBO,
              remark: newVal,
            });
          }}
        />
      </div>
      <div className="flex justify-center w-full mt-6">
        <Button
          onClick={async () => {
            try {
              await updateLicenseCode(codeBO);
              toast('更新成功');
              closeModal();
            } catch (error) {
              toast('更新失败');
              console.error(error);
            }
          }}
        >
          更新
        </Button>
      </div>
    </>
  );
};

/**
 * 返回AG Grid表格，创建一个新的license code，支持备注
 */
export function BikaAdminLicenseCode() {
  const { trpcQuery, trpc } = useApiCaller();
  const curdRef = React.useRef<CrudDatabaseManagerRefHandle>(null);
  const { data: database } = trpcQuery.admin.licenseCode.database.useQuery();

  const deleteLicenseCode = trpcQuery.admin.licenseCode.delete.useMutation();

  return (
    <>
      {database && (
        <CrudDatabaseManager
          ref={curdRef}
          customCreateRecordComponent={(closeModal) => (
            <AddModal
              onClose={() => {
                closeModal();
              }}
            />
          )}
          customRecordDetailComponent={(recordDetail, closeModal) => (
            <LicenseCodeUpdate recordDetail={recordDetail} closeModal={closeModal ?? (() => {})} />
          )}
          value={database}
          getView={(viewId) => ({
            data: database.views.find((view) => view.id === viewId),
            isLoading: false,
            refetch: () => {},
          })}
          getRecord={() => null!}
          listRecords={(dto) => trpc.admin.licenseCode.records.query(dto)}
          useRecordMutation={() => ({
            createRecord: async () => null!,
            updateRecord: async () => null!,
            updateRecords: async () => null!,
            deleteRecords: async (dto) => {
              const deleteIds = dto.recordIds;
              for (const deleteId of deleteIds) {
                await deleteLicenseCode.mutateAsync({
                  name: deleteId,
                });
              }
            },
            isMutating: () => false,
          })}
        />
      )}
    </>
  );
}
