import dynamic from 'next/dynamic';
import { useState, useMemo, useRef } from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { AdminTemplateBasicVO } from '@bika/domains/admin/types/admin-templates';
import { Button } from '@bika/ui/button';
import type { ColDef, ListGridRefHandle } from '@bika/ui/components/list-grid/types';
import { type ILargeTextEditorParams } from '@bika/ui/database/types';
import { useSnackBar } from '@bika/ui/snackbar';
import { TextH2Component } from '@bika/ui/texts';
import { AgGridConfig } from '../../database/client/table-view/ag-grid-config';

const ListGridDeprecated = dynamic(() => import('@bika/ui/components/list-grid/index').then((mod) => mod.ListGrid), {
  ssr: false,
});

interface AdminTemplateBasicFO extends AdminTemplateBasicVO {
  controls: string;
}
export function AdminTemplatesList() {
  const trpcQuery = useTRPCQuery();
  const { toast } = useSnackBar();
  const listGridRef = useRef<ListGridRefHandle>(null);

  const { refetch, isLoading } = trpcQuery.admin.templates.localTemplates.useQuery(undefined, {
    enabled: false,
  });
  const { mutateAsync: sendWarningEmail } = trpcQuery.admin.templates.oneTemplateWarningEmail.useMutation();
  const { mutateAsync: sendAllWarningEmail } = trpcQuery.admin.templates.allTemplatesWarningEmail.useMutation();
  const { mutateAsync: sendAuthorWarningTemplates } =
    trpcQuery.admin.templates.sendAuthorWarningTemplates.useMutation();

  const [colDefs] = useState<ColDef<AdminTemplateBasicFO>[]>([
    { field: 'templateId', headerName: '模板ID' },
    {
      field: 'name',
      headerName: '名字',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      cellRenderer: (props: any) => {
        const tplData = props.data as AdminTemplateBasicFO;
        const str = typeof tplData.name === 'string' ? tplData.name : `${tplData.name['zh-CN']}, ${tplData.name.en}`;

        return <>{str}</>;
      },
    },
    { field: 'author', headerName: '模板负责人' },
    { field: 'visibility', headerName: '模板状态' },
    {
      field: 'score',
      headerName: '模板打分',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      cellRenderer: (props: any) => {
        // const tplData = props.data as AdminTemplateBasicFO;
        const vScore = props.value as number;

        const strScore = `${vScore}`;
        let strLight;
        if (vScore >= 100) {
          strLight = '🟢';
        } else if (vScore < 100 && vScore >= 80) {
          strLight = '🟡';
        } else if (vScore < 80 && vScore >= 60) {
          strLight = '🟠';
        } else {
          strLight = '🔴';
        }

        return (
          <>
            {strScore} {strLight}
          </>
        );
      },
    },
    { field: 'verified', headerName: '认证', cellDataType: 'boolean', editable: true },
    {
      field: 'warningsText',
      headerName: '警告',
      editable: true,
      cellEditor: 'agLargeTextCellEditor',
      cellEditorPopup: true,
      cellEditorParams: {
        maxLength: 1000,
        rows: 15,
        cols: 50,
      } as ILargeTextEditorParams,
    },
    {
      field: 'controls',
      headerName: '控制',
      minWidth: 220,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      cellRenderer: (props: any) => (
        <>
          <Button
            onClick={() => {
              sendWarningEmail({
                templateId: props.data.templateId,
              }).then(() => {
                toast('已经通知作者');
              });
            }}
          >
            通知作者
          </Button>
          <Button
            onClick={() => {
              window.open(`/template/${props.data.templateId}`);
            }}
          >
            查看
          </Button>
          <Button
            onClick={() => {
              // window.open(`/template/${props.data.templateId}`);
              // globalContext.showUIModal({
              //   name: 'resource-editor',
              //   params: {
              //   }
              // });
            }}
          >
            编辑
          </Button>
        </>
      ),
    },
  ]);

  const toolbarArea = (
    <>
      <Button
        onClick={() => {
          sendAllWarningEmail().then(() => {
            toast('已发送集体通知');
          });
        }}
      >
        发送集体通知邮件
      </Button>
      <Button
        onClick={() => {
          sendAuthorWarningTemplates().then(() => {
            toast('已逐个发送通知');
          });
        }}
      >
        逐个作者发送通知邮件
      </Button>
      <Button
        onClick={() => {
          listGridRef.current?.refresh();
          toast('刷新成功');
        }}
      >
        刷新
      </Button>
    </>
  );

  const getRowsData = useMemo(
    () => async () => {
      const fetchResult = await refetch();
      const fetchData = fetchResult.data!;
      const newRowsData = fetchData.map((item) => ({
        templateId: item.templateId,
        name: item.name,
        //   cnName: item.cnName,
        //   enName: item.enName,
        author: item.author,
        visibility: item.visibility,
        score: item.score,
        warningsText: item.warningsText,
        verified: item.verified,
        controls: 'control',
      }));
      return newRowsData;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [refetch],
  );

  return (
    <>
      <div>
        <TextH2Component>模板进度列表</TextH2Component>
        本表自动检查模板填写的配置质量，体验详情则查看 -
        <a href="https://integration.vika.ltd/workbench/dstYQDhySwQFNvZGFi/viw9DSsDPDiSW" target="_blank">
          Vika模板进度表：https://integration.vika.ltd/workbench/dstYQDhySwQFNvZGFi/viw9DSsDPDiSW
        </a>
        ，
      </div>
      <ListGridDeprecated
        ref={listGridRef}
        getRows={getRowsData}
        toolbarArea={toolbarArea}
        paginationPageSizeSelector={AgGridConfig.paginationPageSizeSelector}
        isLoading={isLoading}
        columnsDef={colDefs as ColDef<unknown>[]}
        theme="dark"
      />
    </>
  );
}
