import React, { useEffect } from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n';
import { useSpaceModalContext } from '@bika/domains/space/client/modals/space-modals-context';
import { useDwellTime } from '@bika/domains/system/client/hooks/dwell-time';
import { useSpaceContextForce } from '@bika/types/space/context';
import { Skeleton } from '@bika/ui/skeleton';
import { useSnackBar } from '@bika/ui/snackbar';
import { ReportDetailRenderer } from './report-detail-renderer';

export function ReportDetailView({ reportId }: { reportId: string }) {
  const dwellTimeSeconds = useDwellTime();
  const { toast } = useSnackBar();
  const spaceCtx = useSpaceContextForce();
  const trpcQuery = useTRPCQuery();
  const utils = trpcQuery.useUtils();
  const [read, setRead] = React.useState(true);
  const { t } = useLocale();
  const modalCtx = useSpaceModalContext();

  const { data: vo, isLoading } = trpcQuery.report.fetchReport.useQuery({
    reportId,
  });
  const markReadMutation = trpcQuery.report.markRead.useMutation();
  useEffect(() => {
    setRead(vo?.read || false);
  }, [vo?.read]);

  if (isLoading || !vo) {
    return <Skeleton pos="REPORT_DETAIL" />;
  }
  if (modalCtx) modalCtx.setTitle(vo.subject);
  return (
    <>
      <ReportDetailRenderer
        onRead={() => {
          setRead(true);
          markReadMutation.mutate(
            {
              reportId: vo.id,
              dwellTime: dwellTimeSeconds,
            },
            {
              onSuccess: () => {
                // eslint-disable-next-line no-alert, no-undef
                toast(t.report.read_report);
                utils.my.home.invalidate();
                utils.my.reports.invalidate();
                utils.my.reddots.invalidate();

                // eslint-disable-next-line no-console
                console.log(`Dwell Time: ${dwellTimeSeconds}`);
                spaceCtx.showUIModal(null);
              },
            },
          );
        }}
        vo={vo}
        read={read}
      />
    </>
  );
}
