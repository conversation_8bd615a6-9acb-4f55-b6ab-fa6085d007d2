'use client';

import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { ModuleRegistry } from '@ag-grid-community/core';
import type {
  ColDef,
  GridReadyEvent,
  ValueFormatterParams,
  SizeColumnsToContentStrategy,
  ColGroupDef,
  ICellRendererParams,
  SortDirection,
} from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';
import { LicenseManager } from '@ag-grid-enterprise/core';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { Chip } from '@mui/joy';
import { useCallback, useMemo, useState } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { PivotTableWidgetVO } from '@bika/types/dashboard/vo';
import { formatNumberTypeCellValue } from '@bika/types/system/formatting';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';
import { useGlobalContext } from '@bika/types/website/context';
import { useCssColor } from '@bika/ui/colors';
import { getBgVar, getTextColorByBgVar } from '@bika/ui/components/color-picker/index';
import { Box } from '@bika/ui/layouts';
import { EllipsisText } from '@bika/ui/text/ellipsis';
import { Typography } from '@bika/ui/texts';
import { formatDate } from '../../dashboard/client/renderer/utils';
import { CellValueRenderer } from '../../database/client/cells/cell-renderer/cell-value/cell-value';

interface Props {
  widget: PivotTableWidgetVO;
  locale: ILocaleContext;
}

LicenseManager.setLicenseKey(
  'AgGridLicense66fwc79n[NORMAL][v0102]_NDA3MDk2NjQwMDAwMA==80908dd5fb71b58d3ce28b2ed320216d',
);

ModuleRegistry.registerModules([ClientSideRowModelModule, RowGroupingModule, MenuModule]);

export function PivotTableWidgetVORenderer(props: Props) {
  const { value, settings } = props.widget;
  const { columnField, rowField } = value || { columnField: {}, rowField: {} };
  const fields = useMemo(() => value?.fields || [], [value?.fields]);
  const { t } = props.locale;
  const { theme } = useGlobalContext();
  const colors = useCssColor();

  const { columnDefs: columnDefsVO = [], rowData: rowDataVO = [] } = value || {};

  const formatEmptyValue = useCallback(
    (params: ValueFormatterParams) => {
      if (!params.value) {
        return '-';
      }

      const fieldId = params.colDef?.colId || params.colDef.pivotValueColumn?.getColDef().colId;
      const field = fields.find((f) => f.id === fieldId);

      if (field?.type === 'NUMBER' || field?.type === 'CURRENCY' || field?.type === 'PERCENT') {
        let numericValue: number | null = null;

        if (typeof params.value === 'number') {
          numericValue = params.value;
        } else if (typeof params.value === 'string') {
          const parsed = parseFloat(params.value);
          if (!Number.isNaN(parsed) && Number.isFinite(parsed)) {
            numericValue = parsed;
          }
        }

        if (numericValue !== null) {
          const formattedValue = formatNumberTypeCellValue(field.type, numericValue, field.property);
          return formattedValue || numericValue.toString();
        }
      }
      return params.value;
    },
    [fields],
  );

  const pivotComparator = useCallback(
    (sortOrder?: 'ASC' | 'DESC' | 'DEFAULT') => (valueA: string, valueB: string) => {
      if ((!valueA && !valueB) || !sortOrder || sortOrder === 'DEFAULT') return 0;
      if (!valueA) return sortOrder === 'DESC' ? 1 : -1;
      if (!valueB) return sortOrder === 'DESC' ? -1 : 1;

      const desc = sortOrder === 'DESC';

      const numA = Number(valueA);
      const numB = Number(valueB);

      if (!Number.isNaN(numA) && !Number.isNaN(numB)) {
        return desc ? numB - numA : numA - numB;
      }

      const comp = valueA.localeCompare(valueB);

      return desc ? -comp : comp;
    },
    [],
  );

  const processedColumnDefs = columnDefsVO.map((colDef, index) => {
    let sortValue: SortDirection | undefined;
    if (settings?.rowSort === 'ASC') {
      sortValue = 'asc';
    } else if (settings?.rowSort === 'DESC') {
      sortValue = 'desc';
    }

    const defaultColDef: ColDef = {
      ...colDef,
      valueFormatter: formatEmptyValue,
    };

    if (index === 0) {
      defaultColDef.sort = sortValue;
      defaultColDef.sortable = false;
      switch (rowField?.type) {
        case 'CHECKBOX':
          defaultColDef.cellRenderer = (params: ICellRendererParams) =>
            params.value === '1' ? <CellValueRenderer field={rowField} value={true} /> : '-';
          break;
        case 'RATING':
          defaultColDef.cellRenderer = (params: ICellRendererParams) => (
            <CellValueRenderer field={rowField} value={params.value} />
          );
          break;
        case 'SINGLE_SELECT':
        case 'MULTI_SELECT': {
          if (rowField?.property?.options) {
            const options = rowField.property.options;
            defaultColDef.cellRenderer = (params: ICellRendererParams) => {
              if (!params.value) return '-';

              const rowNames = params.value.split(',');
              const rowValues = options
                .filter((option) => rowNames.includes(option.name as string))
                .map((i) => i.id)
                .filter((i) => i !== undefined);
              return <CellValueRenderer field={rowField} value={rowValues} />;
            };
          }
          break;
        }
        case 'DATETIME':
          defaultColDef.cellRenderer = (params: ICellRendererParams) => {
            if (!params.value) return '-';
            const dateValue = new Date(params.value);
            return formatDate(dateValue, settings?.rowDateFormat);
          };
          break;
        default:
          break;
      }
    }

    const field = fields.find((f) => f.id === colDef.colId);
    if (field?.type === 'NUMBER' || field?.type === 'CURRENCY' || field?.type === 'PERCENT') {
      defaultColDef.cellRenderer = (params: ICellRendererParams) => {
        if (!params.value) return '-';

        if (typeof params.value === 'object') {
          const objValue = params.value;
          if (!objValue.value) {
            return '-';
          }
          return formatNumberTypeCellValue(field.type, objValue, field.property);
        }

        let numericValue: number | null = null;

        if (typeof params.value === 'number') {
          numericValue = params.value;
        } else if (typeof params.value === 'string') {
          const parsed = parseFloat(params.value);
          if (!Number.isNaN(parsed) && Number.isFinite(parsed)) {
            numericValue = parsed;
          }
        }

        if (numericValue !== null) {
          const formattedValue = formatNumberTypeCellValue(field.type, numericValue, field.property);
          return formattedValue || numericValue.toString();
        }

        return params.value.toString();
      };
    }

    // pivot columns sorting
    defaultColDef.pivotComparator = pivotComparator(settings?.columnSort);

    return defaultColDef;
  });

  const [columnDefs] = useState<ColDef[]>(processedColumnDefs);
  const [rowData] = useState<Record<string, unknown>[]>(rowDataVO);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      flex: 1,
      minWidth: 100,
      resizable: true,
      enablePivot: true,
      enableValue: true,
      enableRowGroup: true,
      suppressMenu: true,
      sortable: true,
      unSortIcon: true,
    }),
    [],
  );

  const autoGroupColumnDef = useMemo<ColDef>(
    () => ({
      minWidth: 180,
      pinned: 'left',
      headerName: columnDefs[0]?.field || '',
      wrapText: true,
      autoHeight: true,
      cellRendererParams: {
        totalValueGetter: () => t.ag_grid.pivotColumnGroupTotals,
        suppressCount: true,
      },
    }),
    [columnDefs, t.ag_grid.pivotColumnGroupTotals],
  );

  const onGridReady = useCallback((_params: GridReadyEvent) => {}, []);

  const autoSizeStrategy = useMemo<SizeColumnsToContentStrategy>(
    () => ({
      type: 'fitCellContents',
    }),
    [],
  );

  // pivot mode set custom header group component
  const processPivotResultColGroupDef = useCallback(
    (colDef: ColGroupDef) => {
      const isPivotRowTotal = colDef.groupId?.match(/PivotRowTotal/);
      if (isPivotRowTotal) {
        colDef.headerGroupComponent = () => t.ag_grid.pivotColumnGroupTotals;
        return;
      }
      if (!colDef.headerName) {
        colDef.headerName = '-';
        return;
      }

      switch (columnField?.type) {
        case 'CHECKBOX': {
          const isCheck = colDef.headerName === '1';
          colDef.headerGroupComponent = () =>
            isCheck ? <CellValueRenderer field={columnField} value={isCheck} /> : <>-</>;
          break;
        }
        case 'RATING': {
          const ratingValue = Number(colDef.headerName);
          colDef.headerGroupComponent = () => <CellValueRenderer field={columnField} value={ratingValue} />;
          break;
        }
        case 'SINGLE_SELECT':
        case 'MULTI_SELECT': {
          if (columnField?.property?.options) {
            const options = columnField.property.options;
            const headerNames = colDef.headerName.split(',');
            const chips = options.filter((option) => headerNames.includes(option.name as string));
            colDef.headerGroupComponent = () =>
              chips.map((item) => (
                <Box key={item.id} maxWidth="100%" overflow="hidden" pr={1}>
                  <Chip
                    sx={{
                      background: getBgVar(item.color),
                      color: getTextColorByBgVar(item.color, colors),
                      maxWidth: '100%',
                    }}
                  >
                    <EllipsisText>
                      <Typography textColor={getTextColorByBgVar(item.color, colors)}>{colDef.headerName}</Typography>
                    </EllipsisText>
                  </Chip>
                </Box>
              ));
          }
          break;
        }
        case 'NUMBER':
        case 'CURRENCY':
        case 'PERCENT':
          {
            const numericValue = Number(colDef.headerName);
            if (!Number.isNaN(numericValue) && Number.isFinite(numericValue)) {
              const formattedValue = formatNumberTypeCellValue(columnField.type, numericValue, columnField.property);
              colDef.headerName = formattedValue || colDef.headerName;
            }
          }
          break;
        case 'DATETIME':
          {
            const dateValue = new Date(colDef.headerName);
            colDef.headerName = formatDate(dateValue, settings?.columnDateFormat);
          }
          break;
        default:
          break;
      }
    },
    [columnField, colors, t.ag_grid.pivotColumnGroupTotals],
  );

  const noRowsData = `<div>${t.ag_grid.noRowsToShow}</div>`;

  const themeClassName = useMemo(
    () => (theme?.actualThemeMode === 'light' ? 'ag-theme-quartz' : 'ag-theme-quartz-dark'),
    [theme?.actualThemeMode],
  );

  return (
    <div className="ag-pivot-on" style={{ height: '100%', overflow: 'auto' }}>
      <Box
        sx={{
          height: '100%',
          width: '100%',
          position: 'relative',
          p: 2,
        }}
        className={themeClassName}
      >
        <AgGridReact
          pivotMode={true}
          enableStrictPivotColumnOrder={true}
          columnDefs={columnDefs}
          rowData={rowData}
          defaultColDef={defaultColDef}
          autoGroupColumnDef={autoGroupColumnDef}
          onGridReady={onGridReady}
          grandTotalRow={settings?.showTotalRow ? 'bottom' : undefined}
          columnHoverHighlight
          suppressRowHoverHighlight={false}
          overlayNoRowsTemplate={noRowsData}
          suppressAggFuncInHeader={true}
          autoSizeStrategy={autoSizeStrategy}
          processPivotResultColGroupDef={processPivotResultColGroupDef}
          pivotRowTotals={settings?.showTotalRow ? 'after' : undefined}
        />
      </Box>
    </div>
  );
}
