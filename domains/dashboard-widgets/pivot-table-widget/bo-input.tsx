'use client';

import React, { useState, useMemo } from 'react';
import { getWidgetOptionsConfig } from '@bika/contents/config/client/dashboard/widgets';
import type { ILocaleContext } from '@bika/contents/i18n';
import { STATISTICS_FIELDS_BY_NUMBER_TYPE } from '@bika/domains/dashboard-widgets/chart-widget/bo-input';
import { PivotTableAggregationSchema, widgetDateFormatSchema, type PivotTableWidgetBO } from '@bika/types/dashboard/bo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { Button } from '@bika/ui/button';
import { useCssColor } from '@bika/ui/colors';
import { FieldTypeIconComponent } from '@bika/ui/database/record-detail';
import { DatabaseViewSelect } from '@bika/ui/database/types-form/database-view-select';
import { FormHelperText, FormLabel, IconButton, List, ListItem, ListItemButton, Switch } from '@bika/ui/forms';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import ArrowDownOutlined from '@bika/ui/icons/components/arrow_down_outlined';
import ArrowUpOutlined from '@bika/ui/icons/components/arrow_up_outlined';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import { Box, Stack } from '@bika/ui/layout-components';
import { NodeResourceSelect } from '@bika/ui/node/types-form/node-resource-select';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { Typography } from '@bika/ui/text-components';

interface Props {
  value: PivotTableWidgetBO;
  onChange: (value: PivotTableWidgetBO) => void;
  locale: ILocaleContext;
  api: INodeResourceApi;
  setErrors?: (errors?: Record<string, string>) => void;
}

export function PivotTableWidgetBOInput(props: Props) {
  const { value, locale, api } = props;
  const { datasource } = value;
  const colors = useCssColor();

  const { t, i } = locale;

  const widgetOptions = getWidgetOptionsConfig(locale);

  const [moreSetting, setMoreSetting] = useState(false);
  const { rows, columns, values } = datasource.fields;
  const { data: databaseFields } = api.database.getFieldsBO(datasource.databaseId);
  // static field select options
  const [aggregationOptions, setAggregationOptions] = useState<
    { label: string; value: string; icon?: React.ReactNode }[]
  >([]);
  // common field select options
  const [fieldOptions, setFieldOptions] = useState<{ label: string; value: string; icon?: React.ReactNode }[]>([]);
  const [selectedFieldIds, setSelectedFieldIds] = useState<string[]>([]);

  const columnFieldType = useMemo(
    () => databaseFields?.find((field) => field.id === columns[0]?.fieldId)?.type,
    [databaseFields, columns],
  );

  const rowFieldType = useMemo(
    () => databaseFields?.find((field) => field.id === rows[0]?.fieldId)?.type,
    [databaseFields, rows],
  );

  // set options value
  React.useEffect(() => {
    const staticOptions = [{ label: t.widget_settings.options_config.count_records, value: 'COUNT_RECORDS' }];
    const options = [{ label: t.widget_settings.options_config.none, value: 'none' }];
    if (databaseFields) {
      for (const field of databaseFields) {
        const option = {
          label: i(field.name),
          value: field.id ?? '',
          icon: <FieldTypeIconComponent type={field.type} />,
        };
        options.push(option);

        const isNumberField = STATISTICS_FIELDS_BY_NUMBER_TYPE.includes(field.type);
        if (isNumberField) {
          if (field.type === 'LOOKUP' && field.property?.dataType !== 'NUMBER') continue;
          staticOptions.push(option);
        }
      }
      setAggregationOptions(staticOptions);
      setFieldOptions(options);
    }
  }, [databaseFields]);

  React.useEffect(() => {
    // set selected field ids
    const rowsFieldId = rows[0]?.fieldId;
    const columnsFieldId = columns[0]?.fieldId;
    const valuesFieldIds: string[] = [];

    if (rowsFieldId) {
      valuesFieldIds.push(rowsFieldId);
    }
    if (columnsFieldId) {
      valuesFieldIds.push(columnsFieldId);
    }
    for (const v of values) {
      if (typeof v !== 'string' && v.fieldId && v.fieldId !== 'COUNT_RECORDS') {
        valuesFieldIds.push(v.fieldId);
      }
    }

    setSelectedFieldIds(valuesFieldIds);
  }, [rows, columns, values]);

  if (datasource.type !== 'DATABASE') {
    return null;
  }

  const getFilteredFieldOptions = (
    options: { label: string; value: string; icon?: React.ReactNode }[],
    currentFieldId?: string,
  ) => {
    if (typeof currentFieldId === 'string') {
      return options.filter(
        (option) => option.value === currentFieldId || !selectedFieldIds.includes(option.value ?? ''),
      );
    }
    return options.filter((option) => ![rows[0]?.fieldId, columns[0]?.fieldId].includes(option.value ?? ''));
  };

  const isFieldDeleted = (options: { label: string; value: string; icon?: React.ReactNode }[], fieldId?: string) => {
    if (fieldId) {
      return !getFilteredFieldOptions(options, fieldId).some((option) => option.value === fieldId);
    }
    return false;
  };

  return (
    <>
      <NodeResourceSelect
        required
        setErrors={props.setErrors}
        resourceType="DATABASE"
        locale={locale}
        resourceId={datasource.databaseId}
        setResourceId={(newId) => {
          if (newId) {
            datasource.databaseId = newId;
            props.onChange({
              ...value,
              datasource: {
                type: 'DATABASE',
                databaseId: newId,
                fields: {
                  rows: [],
                  columns: [],
                  values: [
                    {
                      aggregation: PivotTableAggregationSchema.Enum.count,
                      fieldId: 'COUNT_RECORDS',
                    },
                  ],
                },
              },
            });
          }
        }}
        label={t.resource.title_database_id}
      />
      <DatabaseViewSelect
        required
        setErrors={props.setErrors}
        locale={locale}
        api={api}
        databaseId={datasource.databaseId}
        viewId={datasource.viewId}
        setViewId={(newViewId) => {
          if (newViewId) {
            datasource.viewId = newViewId;
            props.onChange({ ...value, datasource });
          }
        }}
        label={t.resource.title_view_id}
        iconSize={16}
      />

      {/* rows select */}
      <Stack mt={1}>
        <FormLabel required={true}> {t.widget_settings.row_dimension_field}</FormLabel>
        <Stack direction="row" flexGrow={1} justifyContent="flex-start" alignItems="center" spacing={1}>
          <SelectInput
            classes={{ root: { flex: 1 } }}
            required
            setErrors={props.setErrors}
            iconSize={16}
            options={getFilteredFieldOptions(fieldOptions, rows[0]?.fieldId)}
            value={rows[0]?.fieldId ?? null}
            onChange={(fieldId) => {
              let newSettings = { ...value.settings };
              if (fieldId) {
                datasource.fields.rows = [{ fieldId }];

                const newFieldType = databaseFields?.find((field) => field.id === fieldId)?.type;
                if (newFieldType === 'DATETIME') {
                  if (!newSettings.rowDateFormat) {
                    newSettings.rowDateFormat = widgetDateFormatSchema.enum.YEAR_MONTH_DAY;
                  }
                } else if (newSettings.rowDateFormat) {
                  const { rowDateFormat, ...restSettings } = newSettings;
                  newSettings = restSettings;
                }
              } else {
                datasource.fields.rows = [{ fieldId: undefined }];
                if (newSettings.rowDateFormat) {
                  const { rowDateFormat, ...restSettings } = newSettings;
                  newSettings = restSettings;
                }
              }

              props.onChange({
                ...value,
                datasource,
                settings: newSettings,
              });
            }}
          />
          {rowFieldType === 'DATETIME' && (
            <SelectInput
              iconSize={16}
              options={widgetOptions.dateFormat}
              value={value.settings?.rowDateFormat || widgetDateFormatSchema.enum.YEAR_MONTH_DAY}
              onChange={(newValue) => {
                props.onChange({
                  ...value,
                  settings: {
                    ...value.settings,
                    rowDateFormat: newValue || widgetDateFormatSchema.enum.YEAR_MONTH_DAY,
                  },
                });
              }}
            />
          )}
        </Stack>

        {isFieldDeleted(fieldOptions, rows[0]?.fieldId) && (
          <FormHelperText sx={{ color: 'var(--status-danger)', marginTop: '0.375rem' }}>
            {t.widget_settings.chart_option_field_had_been_deleted}
          </FormHelperText>
        )}
      </Stack>

      {/* columns select */}
      <Stack mt={1}>
        <FormLabel required={true}> {t.widget_settings.column_dimension_field}</FormLabel>
        <Stack direction="row" flexGrow={1} justifyContent="flex-start" alignItems="center" spacing={1}>
          <SelectInput
            classes={{ root: { flex: 1 } }}
            required
            setErrors={props.setErrors}
            iconSize={16}
            options={getFilteredFieldOptions(fieldOptions, columns[0]?.fieldId)}
            value={columns[0]?.fieldId ?? null}
            onChange={(fieldId) => {
              let newSettings = { ...value.settings };
              if (fieldId) {
                datasource.fields.columns = [{ fieldId }];

                const newFieldType = databaseFields?.find((field) => field.id === fieldId)?.type;
                if (newFieldType === 'DATETIME') {
                  if (!newSettings.columnDateFormat) {
                    newSettings.columnDateFormat = widgetDateFormatSchema.enum.YEAR_MONTH_DAY;
                  }
                } else if (newSettings.columnDateFormat) {
                  const { columnDateFormat, ...restSettings } = newSettings;
                  newSettings = restSettings;
                }
              } else {
                datasource.fields.columns = [{ fieldId: undefined }];
                if (newSettings.columnDateFormat) {
                  const { columnDateFormat, ...restSettings } = newSettings;
                  newSettings = restSettings;
                }
              }

              props.onChange({
                ...value,
                datasource,
                settings: newSettings,
              });
            }}
          />
          {columnFieldType === 'DATETIME' && (
            <SelectInput
              iconSize={16}
              options={widgetOptions.dateFormat}
              value={value.settings?.columnDateFormat || widgetDateFormatSchema.enum.YEAR_MONTH_DAY}
              onChange={(newValue) => {
                props.onChange({
                  ...value,
                  settings: {
                    ...value.settings,
                    columnDateFormat: newValue || widgetDateFormatSchema.enum.YEAR_MONTH_DAY,
                  },
                });
              }}
            />
          )}
        </Stack>

        {isFieldDeleted(fieldOptions, columns[0]?.fieldId) && (
          <FormHelperText sx={{ color: 'var(--status-danger)', marginTop: '0.375rem' }}>
            {t.widget_settings.chart_option_field_had_been_deleted}
          </FormHelperText>
        )}
      </Stack>

      {/* summary select only allow number field */}
      <Stack mt={1}>
        <FormLabel required={true}> {t.widget_settings.summary_by_field}</FormLabel>
        <Stack direction={'column'}>
          {values.map((v, index) => {
            const currentFieldId = typeof v !== 'object' ? 'COUNT_RECORDS' : v.fieldId ?? 'COUNT_RECORDS';
            const hasAggregation = typeof v === 'object' && v.fieldId !== 'COUNT_RECORDS';

            return (
              <>
                <Stack
                  direction="row"
                  justifyContent="flex-start"
                  alignItems="center"
                  mb={1}
                  key={typeof v === 'string' ? index : v.fieldId}
                >
                  <Stack
                    direction="row"
                    flexGrow={1}
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={1}
                    mr={1}
                  >
                    <SelectInput
                      classes={{
                        root: {
                          flex: 1,
                        },
                      }}
                      iconSize={16}
                      options={getFilteredFieldOptions(aggregationOptions)}
                      value={currentFieldId}
                      hiddenOptions={selectedFieldIds}
                      onChange={(newValue) => {
                        const newValues = [...datasource.fields.values];
                        let aggregation = typeof v !== 'string' ? v.aggregation : PivotTableAggregationSchema.Enum.sum;
                        if (newValue === 'COUNT_RECORDS') {
                          aggregation = PivotTableAggregationSchema.Enum.count;
                        }
                        if (newValue !== 'COUNT_RECORDS' && typeof v !== 'string' && v.aggregation === 'count') {
                          aggregation = PivotTableAggregationSchema.Enum.sum;
                        }
                        newValues[index] = {
                          fieldId: newValue ?? '',
                          aggregation,
                        };
                        props.onChange({
                          ...value,
                          datasource: { ...datasource, fields: { ...datasource.fields, values: newValues } },
                        });
                      }}
                    />
                    {hasAggregation && (
                      <SelectInput
                        iconSize={16}
                        options={widgetOptions.aggregationTypes.PIVOT_TABLE}
                        value={v.aggregation || 'sum'}
                        onChange={(newAggregation) => {
                          const newValues = [...datasource.fields.values];
                          newValues[index] = { fieldId: v.fieldId, aggregation: newAggregation || 'sum' };

                          props.onChange({
                            ...value,
                            datasource: {
                              ...datasource,
                              fields: {
                                ...datasource.fields,
                                values: newValues,
                              },
                            },
                          });
                        }}
                      />
                    )}
                  </Stack>

                  {values.length > 1 && (
                    <>
                      <IconButton
                        disabled={index === 0}
                        onClick={() => {
                          const newValues = [...values];
                          newValues.splice(index, 1);
                          newValues.splice(index - 1, 0, v);
                          props.onChange({
                            ...value,
                            datasource: { ...datasource, fields: { ...datasource.fields, values: newValues } },
                          });
                        }}
                      >
                        <ArrowUpOutlined />
                      </IconButton>
                      <IconButton
                        disabled={index === values.length - 1}
                        onClick={() => {
                          const newValues = [...values];
                          newValues.splice(index, 1);
                          newValues.splice(index + 1, 0, v);
                          props.onChange({
                            ...value,
                            datasource: { ...datasource, fields: { ...datasource.fields, values: newValues } },
                          });
                        }}
                      >
                        <ArrowDownOutlined />
                      </IconButton>
                      <IconButton
                        sx={{ ml: 2 }}
                        onClick={() => {
                          values.splice(index, 1);
                          if (typeof v !== 'string' && v.fieldId !== 'COUNT_RECORDS') {
                            setSelectedFieldIds(selectedFieldIds.filter((id) => id !== v.fieldId));
                          }
                          props.onChange({
                            ...value,
                            datasource: { ...datasource, fields: { ...datasource.fields, values } },
                          });
                        }}
                      >
                        <DeleteOutlined color="var(--text-secondary)" />
                      </IconButton>
                    </>
                  )}
                </Stack>
                {isFieldDeleted(aggregationOptions, currentFieldId) && (
                  <FormHelperText sx={{ color: 'var(--status-danger)', marginTop: '0.375rem' }}>
                    {t.widget_settings.chart_option_field_had_been_deleted}
                  </FormHelperText>
                )}
              </>
            );
          })}
        </Stack>
        {values.length < 3 && (
          <Button
            startDecorator={<AddOutlined color={'var(--text-secondary)'} />}
            variant="soft"
            color="neutral"
            sx={{
              marginTop: '8px',
              width: 'fit-content',
            }}
            onClick={() => {
              values.push({
                aggregation: PivotTableAggregationSchema.Enum.count,
                fieldId: 'COUNT_RECORDS',
              });
              props.onChange({
                ...value,
                datasource: { ...datasource, fields: { ...datasource.fields, values } },
              });
            }}
          >
            <Typography level={'b3'}>{t.action.add}</Typography>
          </Button>
        )}
      </Stack>

      <List>
        <ListItem>
          <ListItemButton onClick={() => setMoreSetting(!moreSetting)}>
            <Typography
              endDecorator={
                <Box sx={{ rotate: moreSetting ? 0 : '-90deg' }}>
                  <ChevronDownOutlined color="var(--text-primary)" size={12} />
                </Box>
              }
            >
              {t.widget_settings.more_settings}
            </Typography>
          </ListItemButton>
        </ListItem>
        {moreSetting && (
          <>
            <ListItem>
              <Switch
                checked={value.settings?.showTotalRow}
                onChange={(e) => {
                  props.onChange({
                    ...value,
                    settings: {
                      ...value.settings,
                      showTotalRow: e.target.checked,
                    },
                  });
                }}
              />
              <Typography>{t.widget_settings.show_totals}</Typography>
            </ListItem>

            <SelectInput
              options={widgetOptions.pivotSortOptions}
              label={t.widget_settings.row_dimension_sort_config}
              value={value.settings?.rowSort ?? 'DEFAULT'}
              onChange={(newVal) => {
                const newClone = {
                  ...value,
                  settings: {
                    ...value.settings,
                    rowSort: newVal,
                  },
                } as PivotTableWidgetBO;
                props.onChange(newClone);
              }}
            />

            <SelectInput
              options={widgetOptions.pivotSortOptions}
              label={t.widget_settings.column_dimension_sort_config}
              value={value.settings?.columnSort ?? 'DEFAULT'}
              onChange={(newVal) => {
                const newClone = {
                  ...value,
                  settings: {
                    ...value.settings,
                    columnSort: newVal,
                  },
                } as PivotTableWidgetBO;
                props.onChange(newClone);
              }}
            />
          </>
        )}
      </List>
    </>
  );
}
