import { describe, test, expect, vi } from 'vitest';
import { DatabaseRecordModel } from '@bika/server-orm';
import { DatabaseFieldWithId } from '@bika/types/database/bo';
import { MockContext, waitForMatchToBeMet } from '../../__tests__/mock';
import { DatabaseSO } from '../../database/server';
import { ChangeLogFactory } from '../server/change-log-factory';
import { RecordChangeSO } from '../server/record-change-so';
import { ChangeContext } from '../server/type';

describe('Test get record change bo', async () => {
  const recordChangeSO = RecordChangeSO.initWithContext({} as ChangeContext);
  test('should return the correct record change bo', async () => {
    const previous = {
      id: '1',
      data: {
        text_field: 'test',
        number_field: 18,
        attachment_field: [
          {
            id: '1',
            name: 'test',
            path: '/attachment/test.jpg',
            bucket: 'test',
            mimeType: 'image/jpeg',
            size: 1024,
          },
        ],
        choose_field: true,
        select_field: 'opt_test',
        multi_select_field: ['opt_test1', 'opt_test2'],
        link_field: ['link_record_id'],
      },
      values: {
        select_field: 'test',
        multi_select_field: ['test', 'test2'],
        lookup_field: 'link_record_title',
        formula_field: 'formula_show_value',
        link_field: ['link_record_title'],
      },
      computed: {
        lookup_field: 'link_record_id',
        formula_field: 'formula_data',
      },
    } as DatabaseRecordModel;
    const current = {
      id: '1',
      data: {
        text_field: 'test2',
        number_field: 19,
        attachment_field: [
          {
            path: '/attachment/test2.jpg',
            name: 'test2',
            id: '2',
            bucket: 'test',
            mimeType: 'image/jpeg',
            size: 1024,
          },
        ],
        choose_field: false,
        select_field: 'opt_test2',
        multi_select_field: ['opt_test1', 'opt_test2', 'opt_test3'],
        link_field: ['link_record_1'],
      },
      values: {
        select_field: 'test2',
        multi_select_field: ['test', 'test2', 'test3'],
        lookup_field: 'new_link_record_title',
        formula_field: 'new_formula_show_value',
        link_field: ['link_record_title_1'],
      },
      computed: {
        lookup_field: 'new_link_record_id',
        formula_field: 'new_formula_data',
      },
    } as DatabaseRecordModel;
    const mockFields = [
      {
        id: 'text_field',
        name: 'text_field',
        type: 'SINGLE_TEXT',
      },
      {
        id: 'number_field',
        name: 'number_field',
        type: 'NUMBER',
      },
      {
        id: 'attachment_field',
        name: 'attachment_field',
        type: 'ATTACHMENT',
      },
      {
        id: 'choose_field',
        name: 'choose_field',
        type: 'CHECKBOX',
      },
      {
        id: 'select_field',
        name: 'select_field',
        type: 'SINGLE_SELECT',
      },
      {
        id: 'multi_select_field',
        name: 'multi_select_field',
        type: 'MULTI_SELECT',
      },
      {
        id: 'link_field',
        name: 'link_field',
        type: 'LINK',
      },
      {
        id: 'lookup_field',
        name: 'lookup_field',
        type: 'LOOKUP',
      },
      {
        id: 'formula_field',
        name: 'formula_field',
        type: 'FORMULA',
      },
    ] as DatabaseFieldWithId[];
    // mock recordChangeSO.fields 返回
    vi.spyOn(recordChangeSO, 'fields', 'get').mockReturnValueOnce(mockFields);
    const change = await recordChangeSO.diff(current, previous);
    expect(change).toEqual({
      type: 'DATABASE_RECORD',
      changeType: 'UPDATE',
      recordId: '1',
      data: {
        text_field: { previous: { data: 'test', values: undefined }, current: { data: 'test2', values: undefined } },
        number_field: { previous: { data: 18, values: undefined }, current: { data: 19, values: undefined } },
        attachment_field: {
          previous: {
            data: [
              {
                id: '1',
                name: 'test',
                path: '/attachment/test.jpg',
                bucket: 'test',
                mimeType: 'image/jpeg',
                size: 1024,
              },
            ],
            values: undefined,
          },
          current: {
            data: [
              {
                id: '2',
                name: 'test2',
                path: '/attachment/test2.jpg',
                bucket: 'test',
                mimeType: 'image/jpeg',
                size: 1024,
              },
            ],
            values: undefined,
          },
        },
        choose_field: { previous: { data: true, values: undefined }, current: { data: false, values: undefined } },
        select_field: {
          previous: { data: 'opt_test', values: undefined },
          current: { data: 'opt_test2', values: undefined },
        },
        multi_select_field: {
          previous: { data: ['opt_test1', 'opt_test2'], values: undefined },
          current: { data: ['opt_test1', 'opt_test2', 'opt_test3'], values: undefined },
        },
        lookup_field: {
          previous: { data: 'link_record_id', values: 'link_record_title' },
          current: { data: 'new_link_record_id', values: 'new_link_record_title' },
        },
        formula_field: {
          previous: { data: 'formula_data', values: undefined },
          current: { data: 'new_formula_data', values: undefined },
        },
        link_field: {
          previous: { data: ['link_record_id'], values: ['link_record_title'] },
          current: { data: ['link_record_1'], values: ['link_record_title_1'] },
        },
      },
      fields: [
        {
          id: 'text_field',
          name: 'text_field',
          type: 'SINGLE_TEXT',
          property: undefined,
        },
        {
          id: 'number_field',
          name: 'number_field',
          type: 'NUMBER',
          property: undefined,
        },
        {
          id: 'attachment_field',
          name: 'attachment_field',
          type: 'ATTACHMENT',
          property: undefined,
        },
        {
          id: 'choose_field',
          name: 'choose_field',
          type: 'CHECKBOX',
          property: undefined,
        },
        {
          id: 'select_field',
          name: 'select_field',
          type: 'SINGLE_SELECT',
          property: undefined,
        },
        {
          id: 'multi_select_field',
          name: 'multi_select_field',
          type: 'MULTI_SELECT',
          property: undefined,
        },
        {
          id: 'link_field',
          name: 'link_field',
          type: 'LINK',
          property: undefined,
        },
        {
          id: 'lookup_field',
          name: 'lookup_field',
          type: 'LOOKUP',
          property: undefined,
        },
        {
          id: 'formula_field',
          name: 'formula_field',
          type: 'FORMULA',
          property: undefined,
        },
      ],
    });
  });

  test('add new record', async () => {
    const current = { id: '1', data: { existing_field: 'old', new_field: 'added' } } as DatabaseRecordModel;
    const mockFields = [
      {
        id: 'existing_field',
        name: 'existing_field',
        type: 'SINGLE_TEXT',
      },
      {
        id: 'new_field',
        name: 'new_field',
        type: 'SINGLE_TEXT',
      },
    ] as DatabaseFieldWithId[];
    // mock recordChangeSO.fields 返回
    vi.spyOn(recordChangeSO, 'fields', 'get').mockReturnValueOnce(mockFields);
    const change = await recordChangeSO.diff(current);

    expect(change).toStrictEqual({
      type: 'DATABASE_RECORD',
      changeType: 'CREATE',
      recordId: '1',
      data: {
        existing_field: { data: 'old', values: undefined },
        new_field: { data: 'added', values: undefined },
      },
      fields: [
        {
          id: 'existing_field',
          name: 'existing_field',
          type: 'SINGLE_TEXT',
          property: undefined,
        },
        {
          id: 'new_field',
          name: 'new_field',
          type: 'SINGLE_TEXT',
          property: undefined,
        },
      ],
    });
  });

  test('add new value', async () => {
    const current = { id: '1', data: { existing_field: 'old', new_field: 'added' } } as DatabaseRecordModel;
    const previous = { id: '1', data: { existing_field: 'old' } } as DatabaseRecordModel;
    const mockFields = [
      {
        id: 'existing_field',
        name: 'existing_field',
        type: 'SINGLE_TEXT',
      },
      {
        id: 'new_field',
        name: 'new_field',
        type: 'SINGLE_TEXT',
      },
    ] as DatabaseFieldWithId[];
    // mock recordChangeSO.fields 返回
    vi.spyOn(recordChangeSO, 'fields', 'get').mockReturnValueOnce(mockFields);
    const change = await recordChangeSO.diff(current, previous);
    expect(change).toStrictEqual({
      type: 'DATABASE_RECORD',
      changeType: 'UPDATE',
      recordId: '1',
      data: {
        new_field: { current: { data: 'added', values: undefined }, previous: { data: undefined, values: undefined } },
      },
      fields: [
        {
          id: 'new_field',
          name: 'new_field',
          type: 'SINGLE_TEXT',
          property: undefined,
        },
      ],
    });
  });

  test('delete value', async () => {
    const previous = { id: '1', data: { removed_field: 'value' } } as DatabaseRecordModel;
    const current = { id: '1', data: {} } as DatabaseRecordModel;
    const mockFields = [
      {
        id: 'removed_field',
        name: 'removed_field',
        type: 'SINGLE_TEXT',
      },
    ] as DatabaseFieldWithId[];
    // mock recordChangeSO.fields 返回
    vi.spyOn(recordChangeSO, 'fields', 'get').mockReturnValueOnce(mockFields);
    const change = await recordChangeSO.diff(current, previous);

    expect(change).toStrictEqual({
      type: 'DATABASE_RECORD',
      changeType: 'UPDATE',
      recordId: '1',
      data: {
        removed_field: {
          previous: { data: 'value', values: undefined },
          current: { data: undefined, values: undefined },
        },
      },
      fields: [
        {
          id: 'removed_field',
          name: 'removed_field',
          type: 'SINGLE_TEXT',
          property: undefined,
        },
      ],
    });
  });

  test('deep object change', async () => {
    const current = {
      id: '1',
      data: {
        nested: { a: 2, b: { c: 82 } },
      },
    } as DatabaseRecordModel;
    const previous = {
      id: '1',
      data: {
        nested: { a: 1, b: { c: 2 } },
      },
    } as DatabaseRecordModel;

    const mockFields = [
      {
        id: 'nested',
        name: 'nested',
        type: 'SINGLE_TEXT',
      },
    ] as DatabaseFieldWithId[];
    // mock recordChangeSO.fields 返回
    vi.spyOn(recordChangeSO, 'fields', 'get').mockReturnValueOnce(mockFields);
    const change = await recordChangeSO.diff(current, previous);

    expect(change).toStrictEqual({
      type: 'DATABASE_RECORD',
      changeType: 'UPDATE',
      recordId: '1',
      data: {
        nested: {
          previous: { data: { a: 1, b: { c: 2 } }, values: undefined },
          current: { data: { a: 2, b: { c: 82 } }, values: undefined },
        },
      },
      fields: [
        {
          id: 'nested',
          name: 'nested',
          type: 'SINGLE_TEXT',
          property: undefined,
        },
      ],
    });
  });

  test('array object change', async () => {
    const current = { id: '1', data: { arr: [1, 2] } } as DatabaseRecordModel;
    const previous = { id: '1', data: { arr: [2, 1] } } as DatabaseRecordModel;

    const mockFields = [
      {
        id: 'arr',
        name: 'arr',
        type: 'MULTI_SELECT',
      },
    ] as DatabaseFieldWithId[];
    // mock recordChangeSO.fields 返回
    vi.spyOn(recordChangeSO, 'fields', 'get').mockReturnValueOnce(mockFields);
    const change = await recordChangeSO.diff(current, previous);

    expect(change).toStrictEqual({
      type: 'DATABASE_RECORD',
      changeType: 'UPDATE',
      recordId: '1',
      data: {
        arr: {
          previous: { data: [2, 1], values: undefined },
          current: { data: [1, 2], values: undefined },
        },
      },
      fields: [
        {
          id: 'arr',
          name: 'arr',
          type: 'MULTI_SELECT',
          property: undefined,
        },
      ],
    });
  });
});

describe('Test write record change bo', async () => {
  test.skip('add a new record', async () => {
    const { user, member, rootFolder } = await MockContext.initUserContext();
    const databaseNode = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: {
        en: 'test',
        'zh-CN': '测试',
      },
    });
    const databaseSO = await databaseNode.toResourceSO<DatabaseSO>();
    const fields = databaseSO.getFields();
    const firstField = fields[0];
    const recordSO = await databaseSO.createRecord(user, member, {
      [firstField.id]: 'test',
    });
    await databaseSO.updateRecords(user, [
      {
        recordId: recordSO.id,
        cells: {
          [firstField.id]: 'test01',
        },
      },
    ]);
    await waitForMatchToBeMet(
      async () => {
        const { total } = await ChangeLogFactory.searchLog('DATABASE_RECORD', { id: recordSO.id });
        return total === 2;
      },
      120000,
      3000,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  }, 180000);

  test(
    'change bo to vo',
    {
      timeout: 180000,
    },
    async () => {
      const { user, member, rootFolder } = await MockContext.initUserContext();
      const databaseNode = await rootFolder.createChildSimple(user, {
        resourceType: 'DATABASE',
        name: {
          en: 'test',
          'zh-CN': '测试',
        },
      });
      const databaseSO = (await databaseNode.toResourceSO()) as DatabaseSO;
      const fields = databaseSO.getFields();
      const firstField = fields[0];
      const recordSO = await databaseSO.createRecord(user, member, {
        [firstField.id]: 'test',
      });
      await waitForMatchToBeMet(
        async () => {
          const recordCount = await databaseSO.getRecordsCount();
          return recordCount === 1;
        },
        90000,
        3000,
      ).catch((error: Error) => {
        throw new Error(error.message);
      });
      // 修改记录
      await databaseSO.updateRecords(user, [
        {
          recordId: recordSO.id,
          cells: {
            [firstField.id]: 'test01',
          },
        },
      ]);
      await waitForMatchToBeMet(
        async () => {
          const { total } = await ChangeLogFactory.searchLog('DATABASE_RECORD', { id: recordSO.id });
          const bb = total === 2;
          if (!bb) {
            console.error('Change log not found for record:', recordSO.id, total);
          }
          return bb;
        },
        120000,
        3000,
      ).catch((error: Error) => {
        throw new Error(error.message);
      });
      const { list: recordChangeSO } = await ChangeLogFactory.searchLog('DATABASE_RECORD', { id: recordSO.id });
      const changeVO = await recordChangeSO[1].toVO({ locale: 'zh-CN' });
      expect(changeVO).toStrictEqual({
        id: recordChangeSO[1].id,
        changeType: 'UPDATE',
        user: {
          id: user.id,
          name: user.name,
          avatar: user.avatar,
        },
        createdAt: recordChangeSO[1].model.createdat,
        content: [
          {
            field: {
              id: firstField.id,
              name: firstField.name,
              type: firstField.type,
              property: undefined,
            },
            previousField: undefined,
            data: {
              current: 'test01',
              previous: 'test',
            },
          },
        ],
      });

      const createVO = await recordChangeSO[0].toVO({ locale: 'zh-CN' });
      expect(createVO).toStrictEqual({
        id: recordChangeSO[0].id,
        changeType: 'CREATE',
        user: {
          id: user.id,
          name: user.name,
          avatar: user.avatar,
        },
        createdAt: recordChangeSO[0].model.createdat,
        content: [
          {
            field: {
              id: firstField.id,
              name: firstField.name,
              type: firstField.type,
              property: undefined,
            },
            data: {
              current: 'test',
            },
            previousField: undefined,
          },
        ],
      });
    },
  );
});
