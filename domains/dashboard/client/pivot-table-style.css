.ag-pivot-on .ag-column-first.ag-cell,
.ag-pivot-on .ag-column-first.ag-header-cell {
	border-right: 1px solid var(--border-default);
}

.ag-pivot-on .ag-theme-quartz,
.ag-pivot-on .ag-theme-quartz-dark {
	--ag-row-hover-color: var(--hover) !important;
	--ag-column-hover-color: var(--hover) !important;
}

.ag-pivot-on .ag-root {
	text-align: center;
}

.ag-pivot-on
	:is(.ag-cell-wrapper, .ag-header-cell-label, .ag-header-cell-comp-wrapper) {
	justify-content: center;
}

.ag-pivot-on .ag-cell-value {
	padding: 8px 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: normal;
}

.ag-pivot-on :is(.ag-header-group-text, .ag-group-value, .ag-header-cell-text) {
	font-weight: bold;
	font-size: 13px;
	text-align: center;
}

.ag-pivot-on .ag-header {
	background-color: var(--bg-page);
}

.ag-pivot-on .ag-header-cell-label .ag-sort-order {
  display: none !important;
}

.ag-pivot-on .ag-header-cell.ag-column-first .ag-header-cell-comp-wrapper .ag-sort-indicator-container {
  display: none !important;
}
