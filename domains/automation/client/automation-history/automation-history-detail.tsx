import dayjs from 'dayjs';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import React from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { getActionTypesConfig } from '@bika/contents/config/client/automation/actions';
import { getTriggerTypesConfig } from '@bika/contents/config/client/automation/triggers';
import { useLocale } from '@bika/contents/i18n/context';
import type { AutomationHistoryDetailDrawer } from '@bika/types/space/bo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { getActionIconPath, getTirggerIconPath } from '@bika/ui/automation/types-form/constant';
import { Button } from '@bika/ui/button';
import { Box, Stack } from '@bika/ui/layouts';
import { Modal } from '@bika/ui/modal';
import { Skeleton } from '@bika/ui/skeleton';
import { snackbarShow } from '@bika/ui/snackbar';
import { StackHeaderBarConfig } from '@bika/ui/stack-header-bar';
import { useStackNavigatorContext } from '@bika/ui/stack-navigator';
import { Typography } from '@bika/ui/texts';

const JsonView = dynamic(() => import('@bika/ui/json-view').then((module) => module.JsonView), {
  loading: () => <>loading...</>,
  ssr: false,
});

export function AutomationHistoryDetail() {
  const trpcQuery = useTRPCQuery();
  const utils = trpcQuery.useUtils();
  const stackContext = useStackNavigatorContext();
  const params = stackContext.params;
  const spaceContext = useSpaceContextForce();
  const locale = useLocale();
  const { i, t } = locale;

  const drawer = spaceContext.getUIDrawer() as AutomationHistoryDetailDrawer;

  const id = params.historyRecordId || drawer.historyRecordId;
  const { data, isLoading } = trpcQuery.automation.fetchRunHistoryDetail.useQuery({
    id,
  });
  const triggerTypesConfig = React.useMemo(() => getTriggerTypesConfig(locale), [locale]);
  const actionTypesConfig = React.useMemo(() => getActionTypesConfig(locale), [locale]);

  const [isCancelDelay, setIsCancelDelay] = React.useState(false);
  const cancelDelayAutomation = trpcQuery.automation.cancelDelayAutomation.useMutation();

  const handleCancelDelay = () => {
    setIsCancelDelay(true);
    cancelDelayAutomation.mutate(
      {
        runHistoryId: id,
      },
      {
        onSuccess: () => {
          setIsCancelDelay(false);
          snackbarShow({
            content: t.automation.cancel_delay_success,
            color: 'success',
          });
          // invalidate cache
          utils.automation.fetchRunHistoryDetail.invalidate({
            id,
          });
          utils.automation.runHistoryList.invalidate();
        },
      },
    );
  };

  if (isLoading) {
    return <Skeleton pos="AUTOMATION_HISTORY" />;
  }
  const triggers = data?.triggers || [];
  const actions = data?.actions || [];
  return (
    <Box sx={{ p: 2 }}>
      <StackHeaderBarConfig title={data ? dayjs(data.startAt).format('YYYY-MM-DD HH:mm:ss') : ''} />
      <Typography level="h6" textColor="var(--text-primary)" sx={{ mb: 1 }}>
        {t.automation.trigger.triggers}
      </Typography>
      <Box>
        {triggers.length === 0 && (
          <Typography textColor="var(--text-secondary)" level="b3">
            {t.automation.trigger_history_empty}
          </Typography>
        )}
        {triggers.map((trigger) => (
          <Box key={trigger.id} sx={{ my: 1 }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <Image
                width={32}
                height={32}
                src={getTirggerIconPath(trigger.triggerType, locale)}
                alt={trigger.triggerType}
                style={{ borderRadius: '6px' }}
              />
              <Box flex={1} overflow="hidden">
                <Typography textColor="var(--text-secondary)" level="b4">
                  {triggerTypesConfig[trigger.triggerType].label}
                </Typography>
                <Typography
                  level="h7"
                  textColor="var(--text-primary)"
                  sx={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
                >
                  {i(trigger.description) || t.automation.no_description}
                </Typography>
              </Box>
            </Stack>
            <Box sx={{ mt: 1 }}>
              <JsonView name={t.automation.input} src={trigger.input || {}} displayDataTypes={false} collapsed />
            </Box>
            <Box sx={{ mt: 1 }}>
              {trigger.output && typeof trigger.output === 'object' ? (
                <JsonView name={t.automation.output} src={trigger.output} displayDataTypes={false} collapsed />
              ) : (
                <JsonView
                  name={null}
                  src={{
                    [t.automation.output]: trigger.output,
                  }}
                  displayDataTypes={false}
                />
              )}
            </Box>
          </Box>
        ))}
      </Box>
      <Typography level="h6" textColor="var(--text-primary)" sx={{ mt: 3, mb: 1 }}>
        {t.automation.action.actions}
      </Typography>
      <Box>
        {actions.map((action) => (
          <Box key={action.id} sx={{ mb: 3 }}>
            <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
              <Image
                width={32}
                height={32}
                src={getActionIconPath(action.actionType, locale)}
                alt={action.actionType}
                style={{ borderRadius: '6px' }}
              />
              <Box flex={1} overflow="hidden">
                <Typography textColor="var(--text-secondary)" level="b4">
                  {actionTypesConfig[action.actionType].label}
                </Typography>
                <Typography
                  level="h7"
                  textColor="var(--text-primary)"
                  sx={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
                >
                  {i(action.description) || t.automation.no_description}
                </Typography>
              </Box>
            </Stack>
            <Box sx={{ mt: 1 }}>
              <JsonView name={t.automation.input} src={action.input} displayDataTypes={false} collapsed />
            </Box>
            <Box sx={{ mt: 1 }}>
              {action.output && typeof action.output === 'object' ? (
                <JsonView name={t.automation.output} src={action.output} displayDataTypes={false} collapsed />
              ) : (
                <JsonView
                  name={null}
                  src={{
                    [t.automation.output]: action.output,
                  }}
                  displayDataTypes={false}
                />
              )}
            </Box>
            {action.itemActionRunResults && (
              <Box sx={{ mt: 1 }}>
                <JsonView
                  name={t.automation.item_output}
                  src={action.itemActionRunResults}
                  displayDataTypes={false}
                  collapsed
                />
              </Box>
            )}
            {action.actionType === 'DELAY' && (
              <Button
                fullWidth
                variant="soft"
                color="neutral"
                size="md"
                onClick={() => {
                  Modal.show({
                    type: 'info',
                    title: t.automation.cancel_delay,
                    content: t.automation.cancel_delay_content,
                    okText: t.buttons.confirm,
                    cancelText: t.buttons.cancel,
                    onOk: handleCancelDelay,
                  });
                }}
                sx={{ mt: '12px' }}
                disabled={isCancelDelay || data?.status !== 'DELAY'}
              >
                {t.automation.cancel_delay}
              </Button>
            )}
          </Box>
        ))}
      </Box>
    </Box>
  );
}
