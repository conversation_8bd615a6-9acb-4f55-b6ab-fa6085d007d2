import assert from 'assert';
import { BObuildIdConverter } from '@bika/domains/node/server/bo-builder/bo-build-id-converter';
import { FolderSO } from '@bika/domains/node/server/folder-so';
import { Action, CreateDocumentAction, CreateDocumentActionSchema, type ActionOutput } from '@bika/types/automation/bo';
import { AbstractActionBuilderHandler } from './abstract-action-builder-handler';
import { IActionRunInput, IActionRunContext } from './types';

/**
 * 创建文档（节点或数据单元格）
 */
export class CreateDocumentActionHandler<T extends CreateDocumentAction> extends AbstractActionBuilderHandler<T> {
  override convertInput(input: T['input'], converter: BObuildIdConverter): T['input'] {
    const { parentFolderId, parentFolderTemplateId } = input.createTo;
    const { instanceId, templateId } = converter.convert({
      instanceId: parentFolderId,
      templateId: parentFolderTemplateId,
      notFoundErrMsg: `Automation create document action linked parent folder ${parentFolderId} not in current folder`,
    });
    return {
      ...input,
      createTo: { ...input.createTo, parentFolderId: instanceId, parentFolderTemplateId: templateId },
    };
  }

  override validateBO(action: Action): boolean {
    const { success } = CreateDocumentActionSchema.safeParse(action);
    return success;
  }

  override async fetchRunOutput(input: IActionRunInput, _context: IActionRunContext): Promise<ActionOutput> {
    const action = CreateDocumentActionSchema.parse(input.action);
    const actionInput = action.input;

    // document 暂时只支持创建到节点资源
    assert(actionInput.createTo.createToType === 'NODE_RESOURCE', 'Only support create document to NODE_RESOURCE');

    const createToFolderId = actionInput.createTo.parentFolderId;

    const user = await super.getOperationUser(input);

    // 获取该parent folder
    const folder = await FolderSO.init(createToFolderId);
    assert(folder, `Folder ${createToFolderId} not found`);

    // 检查是否超出配额
    const space = await folder.toNodeSO().getSpace();
    const entitlement = await space.getEntitlement();
    await entitlement.checkUsageExceed({ feature: 'RESOURCES' });

    const newDoc = await folder.createChildSimple(user!, actionInput.documentCreateDTO);
    assert(newDoc, 'Create document failed');

    return {};
  }

  override async doTest(input: IActionRunInput, context: IActionRunContext): Promise<ActionOutput> {
    return this.fetchRunOutput(input, context);
  }
}
