import { Attachment<PERSON> } from '@bika/domains/attachment/server';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FolderSO } from '@bika/domains/node/server/folder-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server';
import { DatabaseField, DatabaseFieldUnionSchema } from '@bika/types/database/bo';
import { ResponseVO, ResponseVOBuilder } from '@bika/types/openapi/vo';
import { iStringParse } from '@bika/types/system';
import { DatabaseCreateReqVO, DatabaseCreateResVO, DatasheetFieldConverter, UploadFileResVO } from '../../types';
import { OpenAPIUtil } from '../util';

export class DatabaseController {
  /**
   * Create Database
   */
  static async createDatabase(
    user: UserSO,
    spaceId: string,
    { name, description, folderId, preNodeId, fields }: DatabaseCreateReqVO,
  ): Promise<ResponseVO<DatabaseCreateResVO>> {
    const space = await SpaceSO.init(spaceId);

    let folderSO: FolderSO;
    if (!folderId) {
      folderSO = await space.getRootFolder();
    } else {
      folderSO = await (await NodeSO.init(folderId)).toResourceSO<FolderSO>();
    }

    // Parse fields
    let databaseFields: DatabaseField[] | undefined;
    if (fields) {
      databaseFields = fields.map((field) => {
        const bikaField = DatasheetFieldConverter.convertFieldVikaToBika(field);
        return DatabaseFieldUnionSchema.parse(bikaField);
      });
    }

    // 检查用量
    const entitlement = await space.getEntitlement();
    await entitlement.checkUsageExceed({ feature: 'RESOURCES' });

    // Do create database
    const databaseNodeSO = await folderSO.createChildSimple(user, {
      name,
      description,
      resourceType: 'DATABASE',
      // preNodeId, 不支持
      fields: databaseFields,
    });
    const db = await databaseNodeSO.toResourceSO<DatabaseSO>();

    const fieldVOs = db.getFields().map((field) => {
      const vo = field.toVO({ locale: user.locale });
      return { ...vo, name: iStringParse(vo.name) };
    });

    return ResponseVOBuilder.success({
      id: databaseNodeSO.id,
      name: iStringParse(databaseNodeSO.name),
      fields: fieldVOs,
    });
  }

  /**
   * Upload File
   */
  static async uploadFile(file: File): Promise<ResponseVO<UploadFileResVO>> {
    const tmpFile = await OpenAPIUtil.createTmpByFile(file);
    try {
      const attachment = await AttachmentSO.createByLocalFile(tmpFile.path);

      // TODO: 补充缺少的返回值
      return ResponseVOBuilder.success({
        size: attachment.size,
        url: attachment.path,
      });
    } finally {
      await tmpFile.clean();
    }
  }
}
