import { NodeSO } from '@bika/domains/node/server/node-so';
import { SpaceMySO } from '@bika/domains/space/server/space-my-so';
import { UserSO } from '@bika/domains/user/server';
import { db } from '@bika/server-orm';
import { AISearchSO } from '../../../../ai/server/ai-search-so';
import { SseSO } from '../../sse/sse-so';
import { OutgoingWebhookSO } from '../outgoing-webhook-so';

export class NodeEventsReceiver {
  public async onCreate(node: NodeSO) {
    return Promise.allSettled([
      AISearchSO.writeNodeSearchIndex(node),
      OutgoingWebhookSO.emit(node.spaceId, {
        eventType: 'ON_NODE_CREATED',
        node: await node.toVO(),
      }),
    ]);
  }

  public async onUpdate(node: NodeSO) {
    AISearchSO.writeNodeSearchIndex(node);
    if (node.model.updatedBy) {
      SseSO.emit(node.model.updatedBy, {
        name: 'node',
        nodeId: node.id,
        spaceId: node.spaceId,
      });
    }
    // Sse.emit('SpaceTree', { type: 'update' | 'refresh', id: node.id });
  }

  public async onMoved(node: NodeSO, oldNode: NodeSO) {
    // 不在同一个目录需要重新计算节点数量
    if (node.parentId !== oldNode.parentId) {
      const oldParent = await NodeSO.initMaybeNull(oldNode.parentId!);
      if (oldParent) {
        await oldParent.updateState();
      }
      const parent = await NodeSO.initMaybeNull(node.parentId!);
      if (parent) {
        await parent.updateState();
      }
    }

    // move to space should add resource index
    if (oldNode.isPrivate && !node.isPrivate) {
      AISearchSO.writeNodeSearchIndex(node);
    }
  }

  // 删除事件
  public async onDelete(node: NodeSO, user: UserSO) {
    SpaceMySO.emitSseSpaceSidebar(user.id, node.spaceId, ['ROOT_NODE']);
    db.search.delete(node.id, 'NODE_RESOURCE', { custom: node.spaceId });
  }
}
