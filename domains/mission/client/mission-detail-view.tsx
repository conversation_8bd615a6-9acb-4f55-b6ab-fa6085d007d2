'use client';

import React, { useEffect } from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { Stack } from '@bika/ui/layouts';
import { Skeleton } from '@bika/ui/skeleton';
import { MissionCreateView } from './mission-create-view';
import { MissionDetailLayoutView } from './mission-detail-layout-view';
// import { MissionDetailStack } from './mission-detail-stack';
import { useSpaceModalContext } from '../../space/client/modals/space-modals-context';

interface Props {
  spaceId: string;
  missionId: string | undefined; // undefined为new
  isModal: boolean;
}

export function MissionDetailView(props: Props) {
  const trpcQuery = useTRPCQuery();
  const modalCtx = useSpaceModalContext();

  const { data: missionVO, isLoading } = trpcQuery.mission.info.useQuery(
    { id: props.missionId! },
    { enabled: props.missionId !== undefined },
  );

  useEffect(() => {
    if (props.isModal && missionVO) {
      modalCtx.setWidth(960);
      modalCtx.setTitle(missionVO.name);
    }

    if (missionVO && missionVO.type === 'READ_TEMPLATE_README' && missionVO.status === 'PENDING') {
      modalCtx?.setShowClose(false);
      modalCtx?.setDisableEscapeKeyDown(true);
      modalCtx?.setDisabledBackdropClick(true);
    }
  }, [props.isModal, missionVO, modalCtx]);

  if (isLoading && props.missionId !== undefined) return <Skeleton pos="MISSION_DETAIL" />;

  const Render = () => {
    // 新建
    if (!props.missionId) {
      return <MissionCreateView />;
    }

    if (!missionVO) return <Skeleton pos="MISSION_DETAIL"></Skeleton>;

    // if (missionVO.type === 'REVIEW_RECORD' && showStack) {
    //   return <MissionDetailStack vo={missionVO as MissionVO} setShowStack={setShowStack} />;
    // }

    return <MissionDetailLayoutView vo={missionVO} isModal={props.isModal} spaceId={props.spaceId} />;
  };

  return (
    <Stack
      sx={{
        width: '100%',
        height: '640px',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      <Render />
    </Stack>
  );
}
