'use client';

import _ from 'lodash';
import type { Route } from 'next';
import React, { useCallback, useState } from 'react';
import toast from 'react-hot-toast';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import {
  CommentRecordMissionSchema,
  MissionUpdateRecordSchema,
  type RedirectSpaceNodeMission,
  ReviewRecordMissionSchema,
  MissionCreateRecordSchema,
  MissionSubmitFormSchema,
  MissionEnterViewSchema,
} from '@bika/types/mission/bo';
import type { MissionVO } from '@bika/types/mission/vo';
import type { SpaceUIModal } from '@bika/types/space/bo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import { Stack } from '@bika/ui/layouts';
import { Modal } from '@bika/ui/modal';
import { MissionVORenderer } from './mission-vo-renderer/mission-vo-renderer';

const { debounce } = _;

interface Props {
  vo: MissionVO;
  isModal: boolean;
  spaceId: string;
}

/**
 * MissionDetail Container，根据类型替换内部组件
 *
 * @param props
 * @returns
 */
export function MissionDetailLayoutView({ vo, isModal, spaceId }: Props) {
  const missionId = vo.id;
  const isUncompleted = vo.status === 'PENDING';
  const [loading, setLoading] = useState(false);
  const uiFramework = useUIFrameworkContext();

  const router = uiFramework.useRouter();
  const searchParams = uiFramework.searchParams;

  const spaceCtx = useSpaceContextForce();

  const { t } = useLocale();

  const trpcQuery = useTRPCQuery();
  const utils = trpcQuery.useUtils();

  const completeMission = trpcQuery.mission.complete.useMutation();
  const rejectMission = trpcQuery.mission.reject.useMutation();
  const deleteMission = trpcQuery.mission.delete.useMutation();

  const invalidateCaches = () => {
    utils.my.reddots.invalidate();
    utils.my.home.invalidate();
    utils.my.todos.invalidate();
    utils.mission.info.invalidate();
    utils.ai.fetchLauncherCommands.invalidate();
  };

  const closeUIModal = () => {
    spaceCtx.showUIModal(null);
  };

  const onClickComplete = debounce(() => {
    completeMission.mutate(
      { spaceId, id: missionId },
      {
        onSuccess: () => {
          toast.success(t.mission.msg_mission_completed, { duration: 1500 });
          invalidateCaches();
          closeUIModal();
        },
      },
    );
  }, 500);

  const onClickReject = debounce(() => {
    toast.success(t.mission.msg_mission_rejected, { duration: 1500 });
    rejectMission.mutate(
      {
        spaceId,
        id: missionId,
      },
      {
        onSuccess: () => {
          invalidateCaches();
          closeUIModal();
        },
      },
    );
  }, 500);

  const redirectToSpaceWithModal = (modal: SpaceUIModal) => {
    setLoading(true);
    const params = btoa(encodeURIComponent(JSON.stringify(modal)));
    // searchParams.set('modal', btoa(encodeURIComponent(JSON.stringify(modal))));
    if (vo.type === 'COMMENT_RECORD') {
      searchParams.set('commentExpanded', 'true');
    }

    setLoading(false);
    window.location.href = `/space/${vo.spaceId}?modal=${params}`;
  };

  const onClickTransfer = () => {
    toast.error(t.mission.msg_transfer_not_supported);
  };

  const showUIModal = (modal: SpaceUIModal, showModal: boolean) => {
    if (showModal) {
      spaceCtx.showUIModal(modal);
    } else {
      redirectToSpaceWithModal(modal);
    }
  };

  const onClickToDetail = useCallback(() => {
    const { databaseId, recordId } = ReviewRecordMissionSchema.parse(vo.bo);
    if (databaseId) {
      let _recordId = recordId;
      // 兼容变量recordId为URL的情况
      if (recordId.startsWith('http')) {
        const splitData = recordId.split('/');
        _recordId = splitData[splitData.length - 1];
      }
      showUIModal({ type: 'record-detail', recordId: _recordId, databaseId }, isModal);
    }
    // eslint-disable-next-line
  }, [isModal, vo.bo]);

  const onClickNextStep = async () => {
    if (vo.type === 'REVIEW_RECORD') {
      if (isModal) {
        onClickComplete();
      } else {
        await completeMission.mutateAsync({
          spaceId,
          id: missionId,
        });
        router.push(`/space/${spaceId}`);
      }
      onClickToDetail();
      return;
    }

    // 任务未完成，且有校验不通过（关联资源不存在）
    if (isUncompleted && vo.invalid) {
      Modal.show({
        type: 'info',
        title: t.mission.modal_title_mission_invalid,
        content: t.mission.modal_content_mission_invalid,
        onOk: () => {
          deleteMission.mutate(
            {
              spaceId,
              id: missionId,
            },
            {
              onSuccess: () => {
                invalidateCaches();
                closeUIModal();
              },
            },
          );
          toast('Task deleted successfully');
        },
      });
      return;
    }

    setLoading(true);
    if (vo.bo.type === 'REDIRECT_SPACE_NODE') {
      await completeMission.mutateAsync({
        spaceId,
        id: missionId,
      });
      invalidateCaches();
      const { nodeId, wizardGuideId } = vo.bo as RedirectSpaceNodeMission;
      const url = `/space/${spaceId}/node/${nodeId}`;
      if (wizardGuideId) {
        localStorage.setItem('WIZARD_GUIDE', JSON.stringify({ id: wizardGuideId }));
      }
      spaceCtx.showUIModal(null);
      router.push(url as Route);
      return;
    }

    if (vo.bo.type === 'READ_TEMPLATE_README') {
      await completeMission.mutateAsync({
        spaceId,
        id: missionId,
      });
      invalidateCaches();

      if (vo.bo.redirect) {
        let url = `/space/${spaceId}`;
        if (vo.bo.redirect.type === 'MY_MISSIONS') {
          url = `/space/${spaceId}/my/todos`;
        } else if (vo.bo.redirect.type === 'MY_REPORTS') {
          url = `/space/${spaceId}/my/reports`;
        } else if (vo.bo.redirect.type === 'SPACE_NODE') {
          const nodeId = vo.bo.redirect.nodeId;
          url = `/space/${spaceId}/node/${nodeId}`;
        }

        if (vo.bo.wizardGuideId) {
          localStorage.setItem('WIZARD_GUIDE', JSON.stringify({ id: vo.bo.wizardGuideId }));
        }
        closeUIModal();
        router.push(url as Route);
      } else {
        closeUIModal();
      }
      return;
    }

    if (vo.type === 'CREATE_RECORD') {
      const { databaseId, mirrorId, viewId } = MissionCreateRecordSchema.parse(vo.bo);
      if (databaseId) {
        showUIModal({ type: 'create-record', viewId, databaseId, mirrorId, disabledBackdropClick: true }, isModal);
      }
      return;
    }
    if (vo.type === 'ENTER_VIEW') {
      const { databaseId, viewId } = MissionEnterViewSchema.parse(vo.bo);
      if (databaseId && viewId) {
        spaceCtx.showUIModal(null);
        const url = `/space/${spaceId}/node/${databaseId}/${viewId}`;
        router.push(url);
      }
      return;
    }
    if (vo.type === 'SUBMIT_FORM') {
      const { formId } = MissionSubmitFormSchema.parse(vo.bo);
      if (formId) {
        spaceCtx.showUIModal(null);
        const url = `/space/${spaceId}/node/${formId}`;
        router.push(url);
      }
      return;
    }
    if (vo.type === 'UPDATE_RECORD') {
      const { databaseId, recordId } = MissionUpdateRecordSchema.parse(vo.bo);
      if (databaseId) {
        showUIModal({ type: 'update-record', recordId, databaseId }, isModal);
      }
      return;
    }
    if (vo.type === 'COMMENT_RECORD') {
      const { databaseId, recordId } = CommentRecordMissionSchema.parse(vo.bo);
      if (databaseId) {
        showUIModal(
          {
            type: 'record-detail',
            recordId,
            databaseId,
            commentExpanded: 'true',
          },
          isModal,
        );
      }
      return;
    }
    //

    if (vo.type === 'INVITE_MEMBER') {
      showUIModal({ type: 'space-settings', tab: { type: 'SPACE_INVITE_MEMBER' } }, isModal);
      return;
    }
    if (vo.type === 'SET_SPACE_NAME') {
      showUIModal({ type: 'space-settings', tab: { type: 'SPACE_SETTING' } }, isModal);
      return;
    }

    onClickComplete();
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  return (
    <Stack flex={1} sx={{ p: 1 }} overflow="hidden">
      <MissionVORenderer
        value={vo}
        isMutating={loading}
        onClickTransfer={onClickTransfer}
        onClickNextStep={onClickNextStep}
        onClickComplete={onClickComplete}
        onClickReject={onClickReject}
      />
    </Stack>
  );
}
