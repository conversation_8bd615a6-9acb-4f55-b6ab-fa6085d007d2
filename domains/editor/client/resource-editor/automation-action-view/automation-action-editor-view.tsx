import React, { useMemo, useEffect, useRef } from 'react';
import { getActionTypesConfig } from '@bika/contents/config/client/automation/actions';
import { useLocale } from '@bika/contents/i18n';
import { ActionSchema, type Action } from '@bika/types/automation/bo';
import type { AutomationActionCreateDTO } from '@bika/types/automation/dto';
import type { AutomationVO, AutomationActionVO } from '@bika/types/automation/vo';
import { useResourceEditorContext } from '@bika/types/editor/context';
import { EditorScreenAutomationActionSchema } from '@bika/types/template/type';
import { Button } from '@bika/ui/button';
import { useUIFrameworkContext } from '@bika/ui/framework';
import { Box } from '@bika/ui/layouts';
import { snackbarShow } from '@bika/ui/snackbar';
import { AutomationActionBOInput, AutomationActionBOInputRef } from './automation-action-bo-input';
import { useDrawerForm } from '../../../../space/client/drawers/drawer-form-context';
import { useResourceEditorStore } from '../store';

export function AutomationActionEditor() {
  const editorContext = useResourceEditorContext();
  const { hasError } = useResourceEditorStore();
  const screenData = EditorScreenAutomationActionSchema.parse(editorContext.getScreen());
  const locale = useLocale();
  const { t } = useLocale();
  const ref = useRef<AutomationActionBOInputRef>(null);

  const autoId = screenData.automationId;
  const parentActionId = screenData.parentActionId === 'undefined' ? undefined : screenData.parentActionId;
  const actionsLength = screenData.actionsLength === 'undefined' ? 0 : Number(screenData.actionsLength);

  const actionsConfig = useMemo(() => getActionTypesConfig(locale), [locale]);

  const [isSaving, setIsSaving] = React.useState(false);

  const { setIsDirty } = useDrawerForm();

  const mutate = editorContext.api.automation.useAutomationMutation();
  const { isMutating } = mutate;
  const [value, setValue] = React.useState<Action>(actionsConfig.WEBHOOK.defaultValue);

  const isNew = useMemo(() => {
    const _isNew = screenData.actionId === 'new';
    if (_isNew && ['FORMAPP_AI', 'TOOLSDK_AI'].includes(value.actionType)) {
      return !value.id;
    }
    return _isNew;
  }, [screenData.actionId, value.actionType, value.id]);

  const isRoundRobin = value.actionType === 'ROUND_ROBIN';

  const { data: fetchedNodeResource, refetch } = editorContext.api.node.getNodeResourceDetail(
    !isNew || isRoundRobin ? autoId : undefined,
  );

  const automationResource = useMemo(() => {
    const resource = fetchedNodeResource?.resource as AutomationVO;
    return resource;
  }, [fetchedNodeResource]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (!autoId) {
      return;
    }
    editorContext.api.automation.updateAutomationVariables(autoId, parentActionId);
  }, [autoId, parentActionId]);

  useEffect(() => {
    if (fetchedNodeResource) {
      let currentAction: AutomationActionVO | undefined;
      if (parentActionId) {
        const parentAction = automationResource?.actions?.find((action) => action.id === parentActionId);
        if (!parentAction) {
          console.log('parentAction not found');
          return;
        }
        currentAction = parentAction.actions?.find((action) => action.id === screenData.actionId);
      } else {
        currentAction = automationResource.actions?.find((action) => action.id === screenData.actionId);
      }
      if (!currentAction) {
        return;
      }
      setValue(currentAction.bo);
    }
    if (isNew) {
      setValue(actionsConfig.WEBHOOK.defaultValue);
    }
  }, [fetchedNodeResource, screenData.actionId, automationResource, actionsConfig.WEBHOOK.defaultValue]);

  const { searchParams } = useUIFrameworkContext();

  const isDebugQuery = useMemo(() => searchParams.get('debug') || 0, []);

  if (!value && !isNew) {
    return <Box>{t.automation.action.not_found}</Box>;
  }

  const handleAddActions = async (actionData: Action, isBack?: boolean) => {
    if (editorContext.api?.onChange) {
      editorContext.api?.onChange({
        type: 'Action',
        input: actionData,
      });
      if (isBack) {
        editorContext.back();
      }
      return;
    }
    try {
      const createDto: AutomationActionCreateDTO = {
        automationId: autoId,
        action: actionData,
      };
      if (parentActionId) {
        createDto.parentActionId = parentActionId;
      }
      await mutate.createAction(createDto);
    } catch (_error) {
      snackbarShow({
        content: 'Submit failed',
        color: 'danger',
      });
      return;
    }
    snackbarShow({
      content: t.resource.create_automation_action_success,
      color: 'success',
    });

    if (isBack) {
      editorContext.back();
    }
  };

  const handleUpdateActions = async (actionData: Action, isBack?: boolean) => {
    if (editorContext.api?.onChange) {
      editorContext.api?.onChange({
        type: 'Action',
        input: actionData,
      });
      if (isBack) {
        editorContext.back();
      }
      return;
    }

    try {
      // TODO:
      await mutate.updateAction({
        actionId: value.id || screenData.actionId,
        action: actionData,
      });
    } catch (_error) {
      snackbarShow({
        content: 'Submit failed',
        color: 'danger',
      });
      return;
    }
    await refetch();
    snackbarShow({
      content: t.resource.update_automation_action_success,
      color: 'success',
    });

    if (isBack) {
      editorContext.back();
    }
  };

  const onSubmit = async (isBack?: boolean) => {
    if (!value) {
      return;
    }

    setIsSaving(true);
    const parseBo = ActionSchema.safeParse(value);
    if (!parseBo.success) {
      console.log('error, ', JSON.stringify(value, null, 2));
      snackbarShow({
        content: 'Submit failed',
        color: 'danger',
      });
      setIsSaving(false);
      return;
    }
    setIsDirty(false);
    if (isNew) {
      await handleAddActions(parseBo.data, isBack);
    } else {
      await handleUpdateActions(parseBo.data, isBack);
    }
    setIsSaving(false);
    editorContext.api.node.invalidateNodeResourceDetail();
  };

  let isComingSoon: boolean = (value.actionType && actionsConfig[value.actionType].display === 'COMING_SOON') || false;
  if (isDebugQuery) {
    isComingSoon = false;
  }

  return (
    <Box>
      {value && (
        <Box
          sx={{
            padding: 2,
            position: 'relative',
            height: 'calc(100vh - 136px)',
            overflowX: 'hidden',
            overflowY: 'auto',
          }}
        >
          <AutomationActionBOInput
            ref={ref}
            value={value}
            onChange={(val) => {
              setIsDirty(true);
              setValue(val);
            }}
            otherActions={automationResource?.actions}
            locale={locale}
            api={editorContext.api}
            uiHandler={editorContext.uiHandler}
            automationId={autoId}
            onSave={() => onSubmit(false)}
            parentActionId={parentActionId}
            actionsLength={actionsLength}
          />
        </Box>
      )}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          height: '72px',
          borderTop: '1px solid var(--border-default)',
        }}
      >
        <Button
          onClick={async () => {
            if (ref.current) {
              if (ref.current.validate) {
                const ret = await ref.current.validate();
                if (!ret) {
                  return;
                }
              }
            }

            onSubmit(true);
          }}
          disabled={isComingSoon || hasError()}
          loading={isMutating || isSaving}
          sx={{
            minWidth: '200px',
          }}
          size="lg"
        >
          {t.action.save}
        </Button>
      </Box>
    </Box>
  );
}
