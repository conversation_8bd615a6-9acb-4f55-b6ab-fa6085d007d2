import React from 'react';
import { useTRPCQuery } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n/context';
import { DatabaseViewBOInput } from '@bika/domains/database/client/database/database-view-bo-input';
import { useDrawerForm } from '@bika/domains/space/client/drawers/drawer-form-context';
import type { View, Database } from '@bika/types/database/bo';
import type { RecordRenderVO } from '@bika/types/database/vo';
import { useResourceEditorContext } from '@bika/types/editor/context';
import { EditorScreenDatabaseViewSchema } from '@bika/types/template/type';
import { useGlobalState } from '@bika/types/website/context';
import { Button } from '@bika/ui/button';
import type { GridApi } from '@bika/ui/database/types';
import { Stack, Box } from '@bika/ui/layouts';
import { Skeleton } from '@bika/ui/skeleton';
import { useSnackBar } from '@bika/ui/snackbar';
import { StackHeaderBarConfig } from '@bika/ui/stack-header-bar';

export function DatabaseViewUpdateDTOForm() {
  const editorContext = useResourceEditorContext();
  const editorProps = EditorScreenDatabaseViewSchema.parse(editorContext.getScreen());

  const { toast } = useSnackBar();

  const locale = useLocale();

  const { t, i } = locale;

  const { setIsDirty } = useDrawerForm();

  const trpcQuery = useTRPCQuery();
  const utils = trpcQuery.useUtils();
  // assert(editorProps.idType === 'DATABASE_VIEW');
  const { viewId, databaseId } = editorProps;

  const { data } = editorContext.api.node.useNodeResourceBO(databaseId);
  const mutate = editorContext.api.database.useDatabaseMutation();
  const database = data as Database;

  const [globalApi, _setGlobalAGGridApi] = useGlobalState<GridApi<RecordRenderVO>>('AG_GRID_API');

  const view: View | undefined = database?.views?.find((item) => item.id === viewId || item.templateId === viewId);

  const [loading, setLoading] = React.useState(false);
  const [saveBtnActived, setSaveBtnActived] = React.useState(false);

  const [viewBO, setViewBO] = React.useState<View>();

  React.useEffect(() => {
    if (view) {
      setViewBO(view);
    }
  }, [view]);

  const updateDatabaseView = async () => {
    if (!viewBO) {
      return;
    }
    setIsDirty(false);
    setLoading(true);
    try {
      await mutate.updateView({
        viewId,
        databaseId,
        data: {
          type: viewBO.type,
          sorts: viewBO.sorts,
          filters: viewBO.filters,
          name: viewBO.name,
          fields: viewBO.fields,
          groups: viewBO.groups,
          extra: viewBO.extra,
        },
      });
    } catch (e) {
      if (e instanceof Error) {
        toast(`Something error: ${e.message}`, { variant: 'error' });
      } else {
        toast(`Something error: ${String(e)}`, { variant: 'error' });
      }
      setLoading(false);
      return;
    }

    utils.node.boInfo.invalidate({
      id: databaseId,
    });
    // refetch();
    setTimeout(() => {
      // 模板变更重新get Records
      globalApi?.refreshServerSide();
    }, 300);
    toast(t('resource.update_view_success', { name: i(viewBO.name) }), { variant: 'success' });

    editorContext.back();
  };

  if (!viewBO) {
    return <Skeleton pos={'EDITOR_VIEW'} />;
  }

  return (
    <Box>
      <Box
        sx={{
          m: 2,
          height: 'calc(100vh - 136px)',
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
      >
        <StackHeaderBarConfig title={t.resource.edit_database_view} />
        <Stack spacing={1}>
          <DatabaseViewBOInput
            api={editorContext.api}
            locale={locale}
            value={viewBO}
            onChange={(result) => {
              setIsDirty(true);
              setViewBO(result);
              setSaveBtnActived(true);
            }}
            onClickField={(field) => {
              editorContext.pushScreen({
                screenType: 'DATABASE_FIELD',
                databaseId,
                viewId,
                fieldId: field.id,
              });
            }}
          />
        </Stack>
      </Box>
      <Box
        sx={{
          textAlign: 'center',
          pt: 2,
          borderTop: '1px solid var(--border-default)',
        }}
      >
        <Button
          loading={loading}
          disabled={loading || !saveBtnActived}
          sx={{
            minWidth: '200px',
          }}
          size="lg"
          onClick={() => updateDatabaseView()}
        >
          {t.buttons.save}
        </Button>
      </Box>
    </Box>
  );
}
