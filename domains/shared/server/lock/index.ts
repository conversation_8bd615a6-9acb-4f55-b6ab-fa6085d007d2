import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { db } from '@bika/server-orm';

const databaseGlobalLockKey = (databaseId: string) => `lock:database:global:${databaseId}`;
const nodeGlobalLockKey = (nodeId: string) => `lock:node:global:${nodeId}`;

export class LockUtil {
  /**
   * 节点全局锁
   * @param nodeId 节点 ID
   * @param cb 回调函数
   * @param options 选项
   * @returns T
   */
  static async nodeGlobalLock<T>(
    nodeId: string,
    cb: () => Promise<T>,
    options?: {
      // 自定义错误
      error?: Error;
      // 续约持续时间，默认 5s
      duration?: number;
      // 重试次数，默认 10 次
      retryCount?: number;
      // 重试间隔，默认 500ms
      retryDelay?: number;
    },
  ): Promise<T> {
    const lockKeys = [nodeGlobalLockKey(nodeId)];

    const error = options?.error ?? null;
    const duration = options?.duration ?? 5000;
    const retryCount = options?.retryCount ?? 10;
    const retryDelay = options?.retryDelay ?? 500;

    try {
      return await db.redis.redlock.using(
        lockKeys,
        duration,
        {
          retryCount,
          retryDelay,
        },
        cb,
      );
    } catch (e) {
      // Redlock 的错误
      if ((e as Error)?.name === 'ExecutionError') {
        // 自定义错误
        if (error) {
          throw error;
        }
        // 默认错误
        else {
          throw new ServerError(errors.node.node_update_too_frequent);
        }
      }

      // 其他错误
      throw e;
    }
  }

  /**
   * 数据库全局锁
   * @param databaseId 数据库 ID
   * @param cb 回调函数
   * @param options 选项
   * @returns T
   */
  static async databaseGlobalLock<T>(
    databaseId: string,
    cb: () => Promise<T>,
    options?: {
      // 自定义错误
      error?: Error;
      // 续约持续时间，默认 5s
      duration?: number;
      // 重试次数，默认 10 次
      retryCount?: number;
      // 重试间隔，默认 500ms
      retryDelay?: number;
    },
  ): Promise<T> {
    const lockKeys = [databaseGlobalLockKey(databaseId)];

    const error = options?.error ?? null;
    const duration = options?.duration ?? 5000;
    const retryCount = options?.retryCount ?? 10;
    const retryDelay = options?.retryDelay ?? 500;

    try {
      return await db.redis.redlock.using(
        lockKeys,
        duration,
        {
          retryCount,
          retryDelay,
        },
        cb,
      );
    } catch (e) {
      // Redlock 的错误
      if ((e as Error)?.name === 'ExecutionError') {
        // 自定义错误
        if (error) {
          throw error;
        }
        // 默认错误
        else {
          throw new ServerError(errors.database.database_locked, {
            databaseId,
          });
        }
      }

      // 其他错误
      throw e;
    }
  }
}
