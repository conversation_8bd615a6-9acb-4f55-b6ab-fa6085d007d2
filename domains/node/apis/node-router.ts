import { z } from 'zod';
import { NodeController } from '@bika/domains/node/apis';
import { UserSO } from '@bika/domains/user/server/user-so';
import { publicProcedure, protectedProcedure, router } from '@bika/server-orm/trpc';
import { NodeResourceSchema } from '@bika/types/node/bo';
import * as T from '@bika/types/node/dto';
import { ExampleTypeSchema, NodeTreeVOSchema } from '@bika/types/node/vo';
import { NodeExampleSO } from '../server/node-example-so';

export const nodeRouter = router({
  /**
   * List a list nodes in a space
   */
  list: protectedProcedure.input(T.NodeListDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return NodeController.listResource(user, input);
  }),

  search: protectedProcedure.input(T.NodeSearchDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return NodeController.searchResource(user, input);
  }),
  /**
   * Retrieve the root node of a space (NodeTreeVO)
   * include "space" and "private" scope
   */
  root: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
      }),
    )
    .output(
      z.object({
        space: NodeTreeVOSchema.nullable(),
        private: NodeTreeVOSchema.nullable(),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const { spaceId } = input;
      return {
        space: await NodeController.retrieveRoot(user, spaceId, 'SPACE'),
        private: await NodeController.retrieveRoot(user, spaceId, 'PRIVATE'),
      };
    }),

  /**
   * 获取权限
   */
  permission: publicProcedure.input(T.NodeInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.retrievePermission(ctx, input);
  }),

  /**
   * Retrieve the simple info of a node
   */
  nodeTree: publicProcedure.input(T.NodeInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;

    return NodeController.retrieveNodeTree(ctx, input);
  }),

  /**
   * 节点属性，用于邮件菜单，查看
   */
  info: protectedProcedure.input(T.NodeInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.retrieveNodeInfo(ctx, input);
  }),

  /**
   * feed，等同 open 激活，是 detail 的 feed 版本
   */
  talk: publicProcedure.input(T.NodeInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.retrieveDetail(ctx, input, true);
  }),

  /**
   * Retrieve the detail vo of a node
   */
  detail: publicProcedure.input(T.NodeInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.retrieveDetail(ctx, input);
  }),

  /**
   * Retrieve the node detail information in the node tree
   */
  position: publicProcedure.input(T.NodeInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.retrievePosition(ctx, input);
  }),

  /**
   * Retrieve the bo info, use for editor
   */
  boInfo: publicProcedure.input(T.NodeInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const resource = await NodeController.retrieveBO(ctx, input);
    const { success, error, data } = NodeResourceSchema.safeParse(resource);
    if (!success) {
      console.error(`boInfo schema parse error`, error);
      return resource;
    }
    return data;
  }),

  // 获取编辑器专用的EditorNodeFolderDTOSchema，一种多态，folder或者template folder详情
  folderDetail: publicProcedure.input(T.NodeInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.retrieveNodeFolderDTO(ctx, input);
  }),
  /**
   * Create a new resource
   */
  create: protectedProcedure.input(T.NodeCreateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.createResource(ctx, input);
  }),

  /**
   * 获取类型resource type类型的examples
   */
  examples: protectedProcedure
    .input(
      z.object({
        exampleType: ExampleTypeSchema,
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      return NodeExampleSO.getExamples(input.exampleType, ctx.locale);
    }),

  /**
   * Delete a resource
   */
  delete: protectedProcedure.input(T.NodeDeleteDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.deleteResource(ctx, input);
  }),

  /**
   * Updates the specific resource by setting the values of the parameters passed
   */
  update: protectedProcedure.input(T.NodeUpdateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.updateResource(ctx, input);
  }),

  /**
   * Updates the resource position, include move private to space
   */
  move: protectedProcedure.input(T.NodeMoveDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.moveResource(ctx, input);
  }),

  /**
   * publish or exort folder
   */
  publish: protectedProcedure.input(T.NodePublishDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.publishTemplate(ctx, input);
  }),

  detach: protectedProcedure.input(T.NodeDeleteDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.detachTemplate(ctx, input);
  }),

  export: protectedProcedure.input(T.NodeExportDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return NodeController.exportResource(ctx, input);
  }),

  bikafilePreview: protectedProcedure.input(T.BikafileImportDTOSchema.omit({ type: true })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return NodeController.bikafilePreview(user, input);
  }),

  // 导入增量数据
  import: protectedProcedure.input(T.NodeImportDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    return NodeController.importResource(ctx, input);
  }),

  /**
   * upgrade template folder.
   */
  upgradeTemplate: protectedProcedure.input(T.NodeUpgradeDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    await NodeController.upgradeTemplateFolder(ctx, input);
  }),

  /**
   * Apply access node
   */
  requestAccess: protectedProcedure.input(T.NodeApplyAccessDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    await NodeController.requestAccess(user, input);
  }),

  /**
   * Validate share password
   */
  sharePasswordValidate: publicProcedure.input(T.NodeSharePasswordValidateDTOSchema).mutation(async (opts) => {
    const { input } = opts;
    return NodeController.sharePasswordValidate(input);
  }),

  toggleShortURL: protectedProcedure
    .input(
      z.object({
        nodeId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      return NodeController.toggleShortURL(ctx, input);
    }),
});
