import assert from 'assert';
import { AutomationExamplesConfig } from '@bika/contents/config/server/automation/example';
import { DatabaseCreateExamples } from '@bika/contents/config/server/database/example';
import { LocalContentLoader } from '@bika/server-orm';
import type { Automation } from '@bika/types/automation/bo';
import type { Database } from '@bika/types/database/bo';
import type { Locale } from '@bika/types/i18n/bo';
import type { ExampleType, ExampleVO } from '@bika/types/node/vo';
import { StoreTemplateSO } from '../../store/server/store-template-so';

export class NodeExampleSO {
  public static async getExamples(exampleType: ExampleType, locale: Locale): Promise<ExampleVO[]> {
    const examples: ExampleVO[] = [];
    if (exampleType === 'DATABASE') {
      for (const cfg of DatabaseCreateExamples) {
        const { templateId, resourceIndex } = cfg;

        const storeTemplate = await StoreTemplateSO.init(templateId);
        const tplRepo = await storeTemplate.getRepo();
        const tpl = tplRepo.current.data;
        const databaseBO = tpl.resources[resourceIndex] as Database;
        if (databaseBO.resourceType !== 'DATABASE') {
          // 打印哪个模版的哪个资源错误
          throw new Error(`Database resource type error: ${templateId} ${resourceIndex}`);
        }
        const exampleVO: ExampleVO = {
          type: 'DATABASE',
          bo: databaseBO,
        };
        examples.push(exampleVO);
      }
    } else if (exampleType === 'AUTOMATION') {
      for (const cfg of AutomationExamplesConfig) {
        const { templateId, resourceIndex } = cfg;

        const storeTemplate = await StoreTemplateSO.init(templateId);
        const tplRepo = await storeTemplate.getRepo();
        const tpl = tplRepo.current.data;
        const automationBO = tpl.resources[resourceIndex] as Automation;
        assert(automationBO.resourceType === 'AUTOMATION');

        if (cfg.name) {
          // override the name with the example config
          automationBO.name = cfg.name;
        }

        if (cfg.description) {
          // override the description with the example config
          automationBO.description = cfg.description;
        }

        const exampleVO: ExampleVO = {
          type: 'AUTOMATION',
          bo: automationBO,
        };
        examples.push(exampleVO);
      }
    } else if (exampleType === 'TEMPLATE') {
      //
      const recommendTemplatesIds = LocalContentLoader.template.getRecommendTemplateIds(locale);
      for (const templateId of recommendTemplatesIds) {
        const storeTemplate = await StoreTemplateSO.init(templateId);
        const tplVO = await storeTemplate.toSimpleVO();
        const exampleVO: ExampleVO = {
          type: 'TEMPLATE',
          bo: tplVO,
        };
        examples.push(exampleVO);
      }
    } else {
      console.error('Unknown example type', exampleType);
    }

    return examples;
  }
}
