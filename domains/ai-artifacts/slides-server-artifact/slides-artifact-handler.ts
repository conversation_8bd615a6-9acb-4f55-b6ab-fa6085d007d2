import assert from 'assert';
// import { generateText } from 'ai';
import { z } from 'zod';
// import { AIModelPicker } from '@bika/domains/ai/server/ai-model-picker';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { IAIModelSelectBO } from '@bika/types/ai/bo';
import {
  ArtifactVO,
  SlidesArtifactSchema,
  SlidesArtifactVO,
  getSlidesArtifactSchemaWithLength,
} from '@bika/types/ai/vo';
import { IStreamResult } from '../../ai/server/types';
import type { UserSO } from '../../user/server/user-so';
import { type AIArtifactSO } from '../ai-artifact-so';
import { IArtifactHandler } from '../interface';

export class SlidesArtifactHandler implements IArtifactHandler {
  private _artifactSO: AIArtifactSO;

  public constructor(artifactSO: AIArtifactSO) {
    this._artifactSO = artifactSO;
  }

  async streamResult<T = z.infer<typeof SlidesArtifactSchema>>(user: UserSO): Promise<IStreamResult<T> | undefined> {
    assert(this._artifactSO.type === 'slides', 'SlidesArtifactHandler only support slides type');

    // const provider = await AIModelPicker.getLanguageModel({ model: 'qwen/qwen-turbo' });
    // const result = await generateText({
    //   model: provider,
    //   prompt: 'Generate a lasagna recipe.',
    // });

    // const model = AISO.getSystemAIModel();
    // const model = 'claude-sonnet-4';
    const model: IAIModelSelectBO = {
      kind: 'preset',
      model: 'openai/gpt-4.1',
    };
    // const model = 'google/gemini-2.5-flash';
    // console.log('model', this._artifactSO.prompt);
    const streamResult = await AISO.streamObject(
      {
        ...this._artifactSO.prompt,
        schema: getSlidesArtifactSchemaWithLength(this._artifactSO.data.outline.slides.length),
        user,
      },
      {
        model,
      },
    );
    return {
      type: 'object',
      objectResult: streamResult,
      model,
    } as IStreamResult<T>;
  }

  /**
   * Get Artifact VO
   *
   * @param user
   * @param streamResult
   * @returns
   */
  async getValue(_user: UserSO, streamResult: IStreamResult | undefined): Promise<ArtifactVO> {
    assert(streamResult, 'streamResult should not be undefined');
    assert(streamResult.type === 'object', 'SlidesArtifactHandler only support object type');

    const vo: ArtifactVO = {
      type: 'slides',
      id: this._artifactSO.id,
      data: {
        slides: [],
      },
    };

    vo.data = (await streamResult.objectResult.object) as SlidesArtifactVO;
    vo.usage = await AISO.parseAICreditCost(streamResult.model, await streamResult.objectResult.usage);
    return vo;
  }
}
