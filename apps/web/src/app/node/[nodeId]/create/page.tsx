'use client';

import { useTRPCQuery } from '@bika/api-caller/context';
import { ModalAsPageContextProvider } from '@bika/domains/database/client/record-detail/modal-as-page-provider';
import { RecordDetailStack } from '@bika/domains/database/client/record-detail/record-detail-stack';
import { SpaceContextProviderWithApi } from '@bika/domains/space/client/context/index';
import { ModalComponent } from '@bika/ui/modal';
import { Skeleton } from '@bika/ui/skeleton';
import { useParams } from 'next/navigation';
import React, { Suspense } from 'react';

export function CreateNodePageInner() {
  const params = useParams<{ nodeId: string }>();

  const nodeId = params.nodeId;
  const trpcQuery = useTRPCQuery();

  const { data, isLoading } = trpcQuery.database.info.useQuery(
    { databaseId: nodeId },
    {
      retry: false,
      refetchOnWindowFocus: false,
      enabled: nodeId != null && nodeId.length > 0,
    },
  );

  if (isLoading) {
    return <Skeleton pos={'NODE_CREATE_PAGE'} />;
  }

  if (!data) {
    return null;
  }

  return (
    <SpaceContextProviderWithApi spaceIdOrSlug={data.spaceId}>
      <ModalAsPageContextProvider closable={false}>
        <ModalComponent closable={false} title={null}>
          <RecordDetailStack databaseId={nodeId} initialStatus={'CREATE'} />
        </ModalComponent>
      </ModalAsPageContextProvider>
    </SpaceContextProviderWithApi>
  );
}

export default function CreateNodePage() {
  return (
    <Suspense fallback={<></>}>
      <CreateNodePageInner />
    </Suspense>
  );
}
