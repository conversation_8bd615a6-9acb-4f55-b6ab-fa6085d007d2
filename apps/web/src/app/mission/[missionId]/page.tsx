'use client';

import { useTRPCQuery } from '@bika/api-caller/context';
import { MissionDetailView } from '@bika/domains/mission/client/mission-detail-view';
import { NoPermissionControl } from '@bika/domains/space/client/no-permission/no-permission-view';
import PageSkeleton from '@bika/domains/website/client/layout/page-skeleton/index';
import { NavHeader, WebAppHeaderComponent } from '@bika/ui/web-layout';
import Box from '@mui/joy/Box';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import React from 'react';

export default function MissionDetailPage(props: { params: Promise<{ missionId: string }> }) {
  // const { useParams } = useSpaceRouter();
  const params = useParams<{ missionId: string }>();
  // TODO: 权限检查

  const trpcQuery = useTRPCQuery();
  const data = trpcQuery.mission.info.useQuery(
    { id: params.missionId },
    {
      retry: false, // 禁用自动重试
      refetchOnWindowFocus: false, // 禁用窗口重新获得焦点时的自动刷新
    },
  );

  if (data.isError) {
    return <NoPermissionControl />;
  }

  if (data.isLoading) {
    return <PageSkeleton loading={data.isLoading} />;
  }
  if (!data.data) {
    return null;
  }

  return (
    <WebAppHeaderComponent
      logo={
        <Link href={`${window.location.origin}/space/${data.data.spaceId}`} target={'_blank'}>
          <Image src={'/assets/icons/logo/bika-logo-text.svg'} alt={'logo'} height={32} width={100} />
        </Link>
      }
    >
      <Box margin="40px 180px" padding="16px" maxWidth={'900px'} bgcolor={'var(--bg-surface)'} borderRadius={'8px'}>
        <NavHeader closable={false}>{data.data.name}</NavHeader>
        <MissionDetailView spaceId={data.data.spaceId} missionId={params.missionId} isModal={false} />
      </Box>
    </WebAppHeaderComponent>
  );
}
