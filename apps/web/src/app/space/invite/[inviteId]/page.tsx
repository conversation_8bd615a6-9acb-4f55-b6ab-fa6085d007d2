'use client';

import { useTRPCQuery } from '@bika/api-caller/context';
import { errors } from '@bika/contents/config/server/error/errors';
import { useLocale } from '@bika/contents/i18n/context';
import { AuthView } from '@bika/domains/auth/client';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { StandalonePage } from '@bika/ui/components/standalone/index';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import { Box, Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

export default function SpaceInvitePage() {
  const { t } = useLocale();
  const params = useParams<{ inviteId: string }>();

  const { inviteId } = params;

  const ctx = useGlobalContext();
  const { Image: UIFrameworkImage } = useUIFrameworkContext();
  const trpcQuery = useTRPCQuery();

  // const [reachLimit, setReachLimit] = useState<UsageExceedLimitErrorData | null>(null);
  const [limitError, setLimitError] = useState<string | null>(null);
  const [canAccepted, setCanAccepted] = useState(true);
  const [invitedEmailMatch, setInvitedEmailMatch] = useState(false);
  const [showLogin, setShowLogin] = useState(false);

  const { data: invitationInfo } = trpcQuery.emailInvitation.info.useQuery({ inviteId });
  const acceptInvitation = trpcQuery.emailInvitation.accept.useMutation();

  useEffect(() => {
    if (invitationInfo) {
      setCanAccepted(invitationInfo.status === 'PENDING');
      setInvitedEmailMatch(invitationInfo.email === ctx.authContext.me?.user.email);
    }
  }, [invitationInfo, ctx]);

  const onAcceptInvitation = () => {
    acceptInvitation.mutate(
      { inviteId },
      {
        onSuccess: () => {
          setShowLogin(false);
          // 不要改push 有些问题
          window.location.href = `/space/${invitationInfo?.space.id}`;
        },
        onError: (err) => {
          if (err?.data?.code === errors.billing.usage_exceed_limit.code) {
            setLimitError(err.data.message);
          }
          toast.error(err?.message);
        },
      },
    );
  };
  if (!invitationInfo) {
    return null;
  }

  const render = () => {
    if (limitError != null) {
      return (
        <>
          <div className=" w-[180px] h-[180px] overflow-hidden border rounded-[10px] !mt-[54px]">
            <UIFrameworkImage
              src="/assets/placeholders/join-space.png"
              width={188}
              height={188}
              alt="join-space placeholder"
            />
          </div>
          <div className="text-h6 text-[--text-primary]">{limitError}</div>
        </>
      );
    }
    if (!canAccepted) {
      return (
        <>
          <Typography level="h3" sx={{ marginBottom: '84px', textAlign: 'center' }}>
            Invite has expired
          </Typography>
        </>
      );
    }
    if (!invitedEmailMatch) {
      return (
        <>
          <Typography level="h3" sx={{ marginBottom: '84px', textAlign: 'center' }}>
            Invited email does not match, please login with the correct email.
          </Typography>
          <Button
            onClick={() => {
              setShowLogin(true);
              setInvitedEmailMatch(true);
            }}
            sx={{
              borderRadius: '30px',
              height: '48px',
              width: '100%',
            }}
          >
            <Typography level="b2">Login</Typography>
          </Button>
        </>
      );
    }

    if (showLogin) {
      return (
        <>
          <Box sx={{ marginTop: 4, marginBottom: 6 }}>
            <Typography level="h1">{t.auth.login_and_register}</Typography>
          </Box>
          <AuthView
            ignoreQuickLogin
            ignoreAutoCreateSpace
            openWindow
            onLogin={async () => {
              ctx.showUIModal(null);
              setShowLogin(false);
            }}
          />
        </>
      );
    }
    return (
      <>
        <div className=" w-[80px] h-[80px] overflow-hidden border rounded-[10px] !mt-[54px]">
          <AvatarImg
            alt="space icon"
            avatar={invitationInfo?.space.logo}
            name={invitationInfo?.space.name || ''}
            shape="SQUARE"
            customSize={AvatarSize.Size80}
          />
        </div>
        <Typography level="h6" sx={{ marginBottom: '84px', textAlign: 'center' }}>
          {invitationInfo?.inviter.name} {t.auth.invite_you_to_join}「{invitationInfo?.space.name}」
        </Typography>
        <Button
          loading={acceptInvitation.isPending}
          onClick={onAcceptInvitation}
          sx={{
            borderRadius: '30px',
            height: '48px',
            width: '100%',
          }}
        >
          <Typography level="b2">{t.action.accept}</Typography>
        </Button>
      </>
    );
  };

  return (
    <StandalonePage>
      <Stack direction="column" p="32px 60px" alignItems="center" bgcolor={'var(--bg-popup)'}>
        {render()}
      </Stack>
    </StandalonePage>
  );
}
