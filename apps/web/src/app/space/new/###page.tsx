'use client';

import { useTRPCQuery } from '@bika/api-caller/context';
import { type SpaceCreateReq, SpaceCreateSchema } from '@bika/types/space/dto';
import { Button } from '@bika/ui/button';
import { Input } from '@bika/ui/forms';
import { useSnackBar } from '@bika/ui/snackbar';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';

/**
 *
 * 新建空间站
 *
 * @returns
 * @deprecated 不要new了，每次都自动创建
 */
export default function SpaceNewPage() {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SpaceCreateReq>({
    resolver: zodResolver(SpaceCreateSchema),
  });
  const trpcQuery = useTRPCQuery();
  const createSpace = trpcQuery.space.create.useMutation();
  const [isRedirecting, setIsRedirecting] = React.useState(false);
  const router = useRouter();

  const { toast } = useSnackBar();
  const onSubmit: SubmitHandler<SpaceCreateReq> = async (data) => {
    const newSpaceVO = await createSpace.mutateAsync(data);
    toast('创建完成', {
      variant: 'success',
    });
    setIsRedirecting(true);
    router.push(`/space/${newSpaceVO.id}`);
  };
  const isLoading = createSpace.isPending || isRedirecting;

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div>
        空间站名称：
        <Input disabled={isLoading} {...register('name', { required: true })} />
        {errors.name?.message as string}
      </div>

      <div>
        <Button loading={isLoading} type="submit">
          创建！
        </Button>
      </div>
    </form>
  );
}
