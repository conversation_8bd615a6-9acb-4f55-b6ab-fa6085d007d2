'use client';

import { useTRPCQuery } from '@bika/api-caller/context';
import { ReportDetailView } from '@bika/domains/report/client/report-detail-view';
import { NoPermissionControl } from '@bika/domains/space/client/no-permission/no-permission-view';
import PageSkeleton from '@bika/domains/website/client/layout/page-skeleton/index';
import { NavHeader, WebAppHeaderComponent } from '@bika/ui/web-layout';
import Box from '@mui/joy/Box';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import React from 'react';

export default function ReportDetailPage() {
  // const { useParams } = useSpaceRouter();
  const params = useParams<{ reportId: string }>();
  const trpcQuery = useTRPCQuery();
  const { isLoading, error, data: report } = trpcQuery.report.fetchReport.useQuery({ reportId: params.reportId });

  if (error) {
    return <NoPermissionControl />;
  }

  if (isLoading) {
    return <PageSkeleton loading={isLoading} />;
  }

  return (
    <>
      {report && (
        <WebAppHeaderComponent
          logo={
            <Link href={`${window.location.origin}/space`} target={'_blank'}>
              <Image src={'/assets/icons/logo/bika-logo-text.svg'} alt={'logo'} height={32} width={100} />
            </Link>
          }
        >
          <Box padding={'16px'} maxWidth={'960px'} bgcolor={'var(--bg-surface)'} borderRadius={'8px'}>
            <NavHeader closable={false}>{report.subject}</NavHeader>
            <ReportDetailView reportId={report.id} />
          </Box>
        </WebAppHeaderComponent>
      )}
    </>
  );
}
