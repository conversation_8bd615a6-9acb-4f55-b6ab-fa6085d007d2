{"name": "@bika/sdk-ts", "private": true, "version": "1.9.3-alpha.38", "description": "Bika.ai Official TypeScript API SDK (internal)", "main": "./src/index.ts", "files": ["dist", "README.md"], "scripts": {"test": "dotenv -e ../../apps/web/.env.local -- vitest", "build": "tsc && node build.mjs build && cp -rf dist ../bika-sdk-js/", "dev": "tsc && node build.mjs watch"}, "repository": {"type": "git", "url": "git+https://github.com/bika-ai/bika.js.git"}, "keywords": ["bika.ai", "bika", "apitable", "aitable", "airtable", "notion"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/bika-ai/bika.js/issues"}, "homepage": "https://github.com/bika-ai/bika.js#readme", "devDependencies": {"@bika/types": "workspace:*", "@swordjs/esbuild-plugin-condition-comment-macro": "^1.0.1", "vitest": "^3.2.4"}, "dependencies": {"axios": "^1.9.0"}}