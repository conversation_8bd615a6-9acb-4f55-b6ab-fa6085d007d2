import { QueryClientProvider } from '@tanstack/react-query';
import { createTRPCReact, type inferReactQueryProcedureOptions, TRPCClient } from '@trpc/react-query';
import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server';
// eslint-disable-next-line import/no-relative-packages
import type { AppRouter } from '../../../apps/server/src/client-types';

// HTTP APIs Server
export const API_ROOT_URL = '/api';
// TRPC Server (with HTTP APIs Server)
export const TRPC_ROOT_URL = `${API_ROOT_URL}/trpc`;

export const trpc: ReturnType<typeof createTRPCReact<AppRouter>> = createTRPCReact<AppRouter>();
// 好像跟 TRPCReactType 一模一样
// export type TRPCReactType = CreateTRPCReact<AppRouter, unknown>;
type TRPCReactType = typeof trpc;
export type { TRPCReactType };

// trpc client, 以下两个好像一模一样
// export type TRPCOriginClient = ReturnType<TRPCReactType['createClient']>;
export type TRPCOriginClient = TRPCClient<AppRouter>;

// infer the types for your router
export type ReactQueryOptions = inferReactQueryProcedureOptions<AppRouter>;
export type RouterInputs = inferRouterInputs<AppRouter>;
export type RouterOutputs = inferRouterOutputs<AppRouter>;

export type { AppRouter };

export { QueryClientProvider };

// 这个也不知道有没有用, 用也是封装起来才行
// export { axios };
