// import assert from 'assert';
import { Memory, type MemoryConfig } from 'mem0ai/oss';
import { PresetLanguageModelServerConfig } from '@bika/contents/config/server/ai/ai-model-config';
import { parseQdrantUrl, qdrantUrl } from '../qdrant/qdrant';
import { TGT_DIM } from '../qdrant/dev-embedding';

const presetModelConfig = PresetLanguageModelServerConfig['openai/gpt-4.1-mini'].alternates![0];

let provider: string = 'openai';
if (presetModelConfig.type === 'AZURE_AI') {
  provider = 'azure_openai';
}
const qdrantUrlConfig = parseQdrantUrl(qdrantUrl);

const config: Partial<MemoryConfig> = {
  llm: {
    provider,
    config: {
      provider,
      baseURL: presetModelConfig.baseUrl,
      apiKey: presetModelConfig.apiKey,
      model: presetModelConfig.modelId,
      // temperature: 0.2,
      // maxTokens: 1500,
    },
  },
  embedder: {
    provider,
    config: {
      // embedder 必须配置 OPENAI_BASE_URL
      apiKey: process.env.OPENAI_API_KEY || '',
      model: 'text-embedding-3-small',
      // Provider-specific settings go here
    },
  },
  disableHistory: true,
  vectorStore: {
    provider: 'qdrant',
    config: {
      collectionName: 'memories',
      embeddingModelDims: TGT_DIM,
      host: qdrantUrlConfig.hostname, // 'localhost',
      port: qdrantUrlConfig.port,
    },
  },
};

export const AIMemorySO = new Memory(config);
