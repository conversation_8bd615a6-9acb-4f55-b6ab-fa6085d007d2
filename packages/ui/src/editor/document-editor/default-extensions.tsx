import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import { Color } from '@tiptap/extension-color';
import Document from '@tiptap/extension-document';
import Highlight from '@tiptap/extension-highlight';
import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import Table from '@tiptap/extension-table';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TableRow from '@tiptap/extension-table-row';
import TaskItem from '@tiptap/extension-task-item';
import TaskList from '@tiptap/extension-task-list';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import Youtube from '@tiptap/extension-youtube';
import { StarterKit } from '@tiptap/starter-kit';
import { common, createLowlight } from 'lowlight';
import type { ILocaleContext } from '@bika/contents/i18n';
import { CustomCodeBlock } from './extensions/custom-code-block';
import { HeadingId } from './extensions/heading-id';
import { ImageResize } from './extensions/image-resizer';
import { UploadImagePlaceholderPlugin } from './extensions/image-resizer/upload-image-placeholder';
import { Mermaid } from './extensions/mermaid';
import { Title } from './extensions/title';
import { Video } from './extensions/video';
import { strings } from './strings';

export const defaultExtensions = (locale: ILocaleContext) => [
  StarterKit.configure({
    history: false,
    document: false,
    dropcursor: {
      color: 'var(--brand)',
      width: 2,
      class: 'drop-cursor',
    },
  }),
  Document.extend({
    content: 'title block*',
  }),
  Mermaid,
  Title,
  Underline,
  TaskList,
  TaskItem.configure({
    nested: true,
  }),
  Link,
  TextStyle,
  TextAlign.configure({
    types: ['heading', 'paragraph', 'image'],
    defaultAlignment: 'left',
  }),
  Color,
  Video,

  // Table
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,

  ImageResize.extend({
    addProseMirrorPlugins() {
      return [UploadImagePlaceholderPlugin()];
    },
  }).configure({
    allowBase64: true,
  }),
  CustomCodeBlock,
  CodeBlockLowlight.configure({
    lowlight: createLowlight(common),
  }),
  Highlight.configure({
    multicolor: true,
  }),
  HeadingId,
  Placeholder.configure({
    placeholder: ({ node, editor }) => {
      if (node.type.name === 'title') {
        return locale.t.document.title_placeholder;
      }
      if (node.type.name === 'heading') {
        return strings[`heading${node.attrs.level}` as keyof typeof strings];
      }
      if (editor.isActive('codeBlock') || node.type.name === 'codeBlock') {
        return locale.t.document.code_placeholder;
      }

      if (editor.isActive('codeBlock') || node.type.name.includes('List')) {
        return locale.t.document.list_placeholder;
      }

      return locale.t.document.text_placeholder;
    },
    includeChildren: false,
  }),
  Youtube.configure({
    controls: false,
    nocookie: true,
  }),
];
