export const strings = {
  // Block-level elements
  paragraph: 'Paragraph',
  heading1: 'Heading 1',
  heading2: 'Heading 2',
  heading3: 'Heading 3',
  heading4: 'Heading 4',
  heading5: 'Heading 5',
  heading6: 'Heading 6',
  image: 'Image',
  unorderedList: 'Unordered list',
  orderedList: 'Ordered list',
  bulletList: 'Unordered list',
  taskList: 'Task list',
  blockquote: 'Quote',
  codeBlock: 'Code block',
  divider: 'Divider',
  table: 'Table',
  // Inline elements
  link: 'Link',
  italic: 'Italic',
  underline: 'Underline',
  strike: 'Strikethrough',
  bold: 'Bold',
  code: 'Inline code',
  mention: 'Mention',
  highlight: 'Highlight',
  // Alignment method
  left: 'Align left',
  center: 'Align center',
  right: 'Align right',
  // Other
  ok: 'Submit',
  cancel: 'Cancel',
  text: 'Text',
  edit: 'Edit',
  unlink: 'Unlink',
  placeholder: 'Editor placeholder',
  commonFormat: 'Common format',
  insertBelow: 'Insert below',
  insertAbove: 'Insert above',
  mediaElement: 'Media element',
  associatedElement: 'Associated element',
  addImage: 'Add image',
  imageSizeError: 'Image limit, { number: 2 }',
  imageTypeError: 'Support image formats',
  imageUploading: 'Image uploading',
  copySuccess: 'Copy success',
  copyFailed: 'Copy failed',
  delete: 'Delete',
  visit: 'Visit',
  linkInputPlaceholder: 'Link input placeholder',
  video: 'Video',
  mermaid: 'Mermaid',
  youtube: 'YouTube',
};
