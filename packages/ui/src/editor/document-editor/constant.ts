import CheckboxOutlined from '../../icons/components/checkbox_outlined';
import ImageOutlined from '../../icons/components/image_outlined';
import LinkOutlined from '../../icons/components/link_outlined';
import VideoOutlined from '../../icons/components/video_outlined';
import BodyFilled from '../../icons/doc_hide_components/body_filled';
import BoldFilled from '../../icons/doc_hide_components/bold_filled';
import CodeFilled from '../../icons/doc_hide_components/code_filled';
import DividingLineFilled from '../../icons/doc_hide_components/dividing_line_filled';
import Headline1Filled from '../../icons/doc_hide_components/headline_1_filled';
import Headline2Filled from '../../icons/doc_hide_components/headline_2_filled';
import Headline3Filled from '../../icons/doc_hide_components/headline_3_filled';
import HighlightFilled from '../../icons/doc_hide_components/highlight_filled';
import ItalicsFilled from '../../icons/doc_hide_components/italics_filled';
import MermaidOutlined from '../../icons/doc_hide_components/mermaid_outlined';
import OrderedFilled from '../../icons/doc_hide_components/ordered_filled';
import QuoteFilled from '../../icons/doc_hide_components/quote_filled';
import StrikethroughFilled from '../../icons/doc_hide_components/strikethrough_filled';
import TextLeftFilled from '../../icons/doc_hide_components/text_left_filled';
import TextMiddleFilled from '../../icons/doc_hide_components/text_middle_filled';
import TextRightFilled from '../../icons/doc_hide_components/text_right_filled';
import UnderlineFilled from '../../icons/doc_hide_components/underline_filled';
import UnorderedFilled from '../../icons/doc_hide_components/unordered_filled';

export enum ElementType {
  PARAGRAPH = 'paragraph',
  HEADING_ONE = 'heading1',
  HEADING_TWO = 'heading2',
  HEADING_THREE = 'heading3',
  HEADING_FOUR = 'heading4',
  HEADING_FIVE = 'heading5',
  HEADING_SIX = 'heading6',
  UNORDERED_LIST = 'unorderedList',
  ORDERED_LIST = 'orderedList',
  LIST_ITEM = 'listItem',
  BULLET_LIST = 'bulletList',
  TASK_LIST_ITEM = 'taskListItem',
  TASK_LIST = 'taskList',
  CODE = 'code',
  CODE_BLOCK = 'codeBlock',
  QUOTE = 'blockquote',
  QUOTE_ITEM = 'quoteItem',
  IMAGE = 'image',
  VIDEO = 'video',
  MERMAID = 'mermaid',
  LINK = 'link',
  MENTION = 'mention',
  DIVIDER = 'divider',
  TABLE_CELL = 'tableCell',
  HIGHLIGHT = 'highlight',

  ITALIC = 'italic',
  STRIKE_THROUGH = 'strike',
  BOLD = 'bold',
  UNDERLINE = 'underline',

  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',

  YOUTUBE = 'youtube',
}

export const ELEMENT_ICONS = {
  [ElementType.PARAGRAPH]: BodyFilled,
  [ElementType.HEADING_ONE]: Headline1Filled,
  [ElementType.HEADING_TWO]: Headline2Filled,
  [ElementType.HEADING_THREE]: Headline3Filled,
  [ElementType.HEADING_FOUR]: Headline3Filled,
  [ElementType.HEADING_FIVE]: Headline3Filled,
  [ElementType.HEADING_SIX]: Headline3Filled,
  [ElementType.ORDERED_LIST]: OrderedFilled,
  [ElementType.UNORDERED_LIST]: UnorderedFilled,
  [ElementType.IMAGE]: ImageOutlined,
  [ElementType.VIDEO]: VideoOutlined,
  [ElementType.YOUTUBE]: VideoOutlined,
  [ElementType.MERMAID]: MermaidOutlined,
  [ElementType.BULLET_LIST]: UnorderedFilled,
  [ElementType.QUOTE]: QuoteFilled,
  [ElementType.DIVIDER]: DividingLineFilled,
  [ElementType.CODE]: CodeFilled,
  [ElementType.CODE_BLOCK]: CodeFilled,
  [ElementType.LINK]: LinkOutlined,
  [ElementType.ITALIC]: ItalicsFilled,
  [ElementType.BOLD]: BoldFilled,
  [ElementType.UNDERLINE]: UnderlineFilled,
  [ElementType.STRIKE_THROUGH]: StrikethroughFilled,
  [ElementType.TASK_LIST]: CheckboxOutlined,
  [ElementType.HIGHLIGHT]: HighlightFilled,
  // Alignment method
  [ElementType.LEFT]: TextLeftFilled,
  [ElementType.CENTER]: TextMiddleFilled,
  [ElementType.RIGHT]: TextRightFilled,
};

export const INLINE_BASIC_ELEMENT = [
  ElementType.PARAGRAPH,
  ElementType.HEADING_ONE,
  ElementType.HEADING_TWO,
  ElementType.HEADING_THREE,
  ElementType.BULLET_LIST,
  ElementType.ORDERED_LIST,
  ElementType.TASK_LIST,
  ElementType.QUOTE,
  ElementType.CODE_BLOCK,
];

export const ALIGN_ELEMENT = [ElementType.LEFT, ElementType.CENTER, ElementType.RIGHT];

export const BASIC_ELEMENT = [
  ElementType.PARAGRAPH,
  ElementType.HEADING_ONE,
  ElementType.HEADING_TWO,
  ElementType.HEADING_THREE,
  ElementType.BULLET_LIST,
  ElementType.ORDERED_LIST,
  ElementType.TASK_LIST,
  ElementType.QUOTE,
  ElementType.DIVIDER,
  ElementType.CODE_BLOCK,
  ElementType.IMAGE,
  ElementType.VIDEO,
  ElementType.MERMAID,
  ElementType.YOUTUBE,
];

export const MENU_ELEMENTS = [
  ElementType.BOLD,
  ElementType.ITALIC,
  ElementType.STRIKE_THROUGH,
  ElementType.UNDERLINE,
  ElementType.CODE,
];

export const HIGHLIGHT_COLORS = [
  'var(--text-primary)',
  'var(--rainbow-purple5)',
  'var(--rainbow-orange5)',
  'var(--rainbow-teal5)',
  'var(--rainbow-gray5)',
  'var(--rainbow-brown5)',
  'var(--rainbow-red5)',
  'var(--rainbow-indigo5)',
  'var(--rainbow-pink5)',
];

export const HIGHLIGHT_BACKGROUNDS = [
  'var(--bg-surface)',
  'var(--rainbow-purple5)',
  'var(--rainbow-orange5)',
  'var(--rainbow-teal5)',
  'var(--rainbow-gray5)',
  'var(--rainbow-brown5)',
  'var(--rainbow-red5)',
  'var(--rainbow-indigo5)',
  'var(--rainbow-pink5)',
];
