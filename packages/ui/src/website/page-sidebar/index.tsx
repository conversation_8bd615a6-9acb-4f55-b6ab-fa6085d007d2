'use client';

import Image from 'next/image';
import Link from 'next/link';
import React, { useMemo } from 'react';
import { LocaleStringConfig } from '@bika/contents/config/client';
import { useLocale } from '@bika/contents/i18n/context';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button';
import { AvatarImg } from '@bika/ui/components/avatar/index';
import { PopoverContent, PopoverTrigger, Popover } from '@bika/ui/components/popover/index';
import { MenuList, MenuItem } from '@bika/ui/forms';
import { useUIFrameworkContext } from '@bika/ui/framework';
import ArrowRightOutlined from '@bika/ui/icons/components/arrow_right_outlined';
import WebOutlined from '@bika/ui/icons/components/web_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { BikaLogo } from '@bika/ui/logo';
import { Typography } from '@bika/ui/texts';
import { AppStoreIconsPath } from '../app-store-icon-path';
import { LinkIcon } from './link';
import { MenuNavItem } from './menu';

export interface IMenu {
  icon: React.FC;
  text: string;
  href: string;
  target?: string;
  exact?: boolean;
}

export interface PageSidebarProps {
  topSideBarSlot?: React.ReactNode;

  topArea?: React.ReactNode;
  bottomArea?: React.ReactNode;

  // menus data
  menuData: IMenu[];
  // 是否不显示popover区域，如不显示多语言切换
  disableLanguage?: boolean;
  onClickItem?: (key?: string) => void;
}
/**
 * Website Sidebar
 *
 * @param param0
 * @returns
 */
export function PageSidebar({
  topSideBarSlot: sideBarSlot,
  menuData: data,
  disableLanguage,
  onClickItem,
}: PageSidebarProps) {
  const uiCtx = useUIFrameworkContext();
  const { usePathname, searchParams } = uiCtx;
  const context = useGlobalContext();
  const pathname = usePathname();
  const fullPathname = useMemo(() => {
    const searchParamsStr = searchParams.toString();
    return `${pathname}${searchParamsStr ? `?${searchParamsStr}` : ''}`;
  }, [searchParams, pathname]);

  const { t, lang } = useLocale();

  const iconsPath = lang === 'zh-CN' ? AppStoreIconsPath.CN : AppStoreIconsPath.National;

  return (
    <Stack
      sx={{
        height: '100%',
        width: '100%',
        borderRight: '1px solid var(--border-default)',
      }}
    >
      <Box
        display={'flex'}
        flexDirection={'column'}
        justifyContent="space-between"
        sx={{
          overflow: 'auto',
          width: uiCtx.isMobile ? '100%' : 280,
          height: '100%',
        }}
      >
        <Box display="flex" flexDirection="column" flex={1} overflow={'hidden'}>
          {/* top */}
          <Box p={2} display="flex" alignItems="center">
            <Box pr={2}>
              <BikaLogo showBackground color="var(--text-primary)" width="46" height="46" />
            </Box>
            <Box>
              <Typography level={'h5'}>
                <Link href="/">{t.brand.brand}</Link>
              </Typography>
              <Typography fontStyle={{ color: 'var(--text-secondary)' }} level={'h8'}>
                {t.slogan.slogan_prd_m}
              </Typography>
            </Box>
          </Box>
          <Box flex={1} sx={{ overflow: 'auto' }}>
            {sideBarSlot}
            {!sideBarSlot && (
              <Box p={{ xs: 0, md: 2 }} flex={1}>
                {data.map((item, index) => (
                  <MenuNavItem
                    onClick={() => {
                      onClickItem?.(item.href);
                    }}
                    target={item.target}
                    key={index}
                    active={item.exact ? fullPathname === item.href : fullPathname.includes(item.href)}
                    text={item.text}
                    icon={item.icon}
                    href={item.href}
                  />
                ))}

                {/* 多语言切换 */}

                {!disableLanguage && (
                  <Popover placement="bottom">
                    <PopoverTrigger>
                      <MenuNavItem text={t.website.change_region} icon={WebOutlined} href="#" />
                    </PopoverTrigger>
                    <PopoverContent>
                      <MenuList variant="outlined" size="lg" sx={{ width: 260 }}>
                        {Object.keys(LocaleStringConfig[lang]).map((locale) => {
                          // @ts-expect-error locale
                          const text = LocaleStringConfig[lang][locale];
                          // console.log(locale, lang);
                          return (
                            <MenuItem
                              key={locale}
                              onClick={() => {
                                if (window.location.pathname === '/') {
                                  window.location.href = `/${locale}`;
                                  return;
                                }
                                if (lang === 'en') {
                                  // 追加第一层路径/locale 不是替换
                                  window.location.href = `/${locale}${window.location.pathname}`;
                                } else {
                                  // 替换的正则 比如/zh-CN/xxx 替换成/en/xxx 当然也可能是 /jp/xxx
                                  window.location.href = window.location.pathname.replace(/^\/[^/]+/, `/${locale}`);
                                }
                              }}
                              color="primary"
                              sx={{ color: 'var(--text-primary)' }}
                            >
                              {text}
                            </MenuItem>
                          );
                        })}
                      </MenuList>
                    </PopoverContent>
                  </Popover>
                )}
                {context?.authContext.isLogin && (
                  <Button
                    sx={{
                      marginTop: '4px',
                      marginBottom: '16px',
                      fontWeight: 'normal',
                    }}
                    fullWidth
                    onClick={() => {
                      window.location.href = '/space';
                    }}
                  >
                    <Stack alignItems="center" justifyContent="center" direction="row">
                      <Stack mr={1}>{t.space.goto_space}</Stack> <ArrowRightOutlined color="var(--static)" />
                    </Stack>
                  </Button>
                )}

                <Stack
                  sx={{
                    cursor: 'pointer',
                    margin: '0 auto',
                    position: 'relative',
                  }}
                  onClick={() => {
                    if (context) {
                      context.showUIModal({ name: 'HELP_VIDEO', videoType: 'PRESENTATION' });
                    } else {
                      console.error('global context is null');
                    }
                  }}
                >
                  <Image
                    alt=""
                    width={248}
                    height={160}
                    src={`/assets/images/tutorial-video/tutorial-cover-${lang}.png`}
                  />
                </Stack>
              </Box>
            )}
          </Box>
        </Box>

        <Box>
          {/* 社交媒体 */}
          <Box mx={4} mb={1} display="flex" justifyContent="space-around" alignItems="center">
            {iconsPath
              .filter((item) => item.url)
              .map((item) => (
                <LinkIcon key={item.name} icon={item.path} link={item.url} />
              ))}
          </Box>

          <Box
            p={1}
            sx={{
              border: context?.authContext.isLogin ? '1px solid var(--border-default)' : null,
            }}
          >
            {context?.authContext.isLogin ? (
              <Popover placement="top">
                <PopoverTrigger>
                  <Box>
                    <Box display="flex" alignItems="center">
                      <Box>
                        {context.authContext.me?.user.avatar && (
                          <AvatarImg
                            size={'md'}
                            name={context.authContext.me.user.name ?? ''}
                            avatar={context.authContext.me.user.avatar}
                          />
                        )}
                      </Box>
                      <Box ml={1}>
                        <Typography
                          sx={{
                            width: 200,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            textAlign: 'left',
                          }}
                          component="span"
                          level="h7"
                        >
                          {context.authContext.me?.user.name}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </PopoverTrigger>
                <PopoverContent>
                  <MenuList
                    // variant="outlined"
                    size="lg"
                    sx={{
                      width: 260,
                    }}
                  >
                    <MenuItem
                      onClick={() => {
                        window.open('/space', '_blank');
                      }}
                      color="primary"
                      sx={{
                        color: 'var(--text-primary)',
                      }}
                    >
                      {t.space.space}
                    </MenuItem>
                    <MenuItem
                      sx={{
                        color: 'var(--text-primary)',
                        '&:hover': {
                          // backgroundColor: 'var(--bg-controls-hover)',
                        },
                      }}
                      color="primary"
                      onClick={() => context.authContext.logout(window.location.href)}
                    >
                      {t.auth.logout}
                    </MenuItem>
                  </MenuList>
                </PopoverContent>
              </Popover>
            ) : (
              <Button
                sx={{ width: '100%' }}
                variant="solid"
                size="lg"
                onClick={() => {
                  context.showUIModal({
                    name: 'AUTH',
                    redirect: window.location.href,
                    onLoginAction: 'CLOSE_MODAL',
                    //   onLogin: () => void context.showUIModal(null),
                  });
                }}
              >
                {t.auth.sign_in}
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </Stack>
  );
}
